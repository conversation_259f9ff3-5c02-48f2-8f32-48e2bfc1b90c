# Dialog Management Patterns - Odoo 18

## 🎯 **Overview**

This guide provides proven patterns for managing dialogs in Odoo 18, based on successful implementation of employee selection popups with PIN validation.

## 🔧 **Critical Dialog Closing Pattern**

### **The Problem**
Individual dialog closing methods (`dialog.close()`, `pinDialog.close()`) are unreliable and may fail, especially with nested dialogs.

### **The Solution: env.services.dialog.closeAll()**
```javascript
// ✅ CORRECT: Use closeAll() for reliable dialog management
env.services.dialog.closeAll(); // Closes all open dialogs reliably

// ❌ WRONG: Individual dialog closing may fail
const dialog = env.services.dialog.add(...);
dialog.close(); // May not work consistently
```

## 📋 **Dialog Lifecycle Patterns**

### **1. Single Dialog Pattern**
```javascript
// Create dialog
const dialog = env.services.dialog.add(
    DialogWrapper,
    {
        Component: MyComponent,
        componentProps: {
            onClosePopup: async (popupId) => {
                console.log("Dialog closing");
                env.services.dialog.closeAll(); // ✅ Reliable closing
            },
            onConfirm: async (data) => {
                // Process data
                await handleConfirmation(data);
                
                // Close dialog after processing
                env.services.dialog.closeAll();
                
                // Optional: Close parent window/wizard
                env.services.action.doAction({ type: 'ir.actions.act_window_close' });
            }
        }
    }
);
```

### **2. Nested Dialog Pattern (Employee + PIN)**
```javascript
// Parent dialog (Employee Selection)
const employeeDialog = env.services.dialog.add(
    DialogWrapper,
    {
        Component: SelectionPopup,
        componentProps: {
            onSelectEmployee: async (employeeId, pin) => {
                if (!pin && employee.requiresPin) {
                    // Open nested PIN dialog
                    env.services.dialog.add(
                        DialogWrapper,
                        {
                            Component: PinPopup,
                            componentProps: {
                                onPinValidate: async (empId, pinCode) => {
                                    const valid = await validatePin(empId, pinCode);
                                    if (valid) {
                                        // Close ALL dialogs (both PIN and employee selection)
                                        env.services.dialog.closeAll();
                                        
                                        // Continue with business logic
                                        await processEmployeeSelection(empId);
                                        return true;
                                    }
                                    return false;
                                },
                                onClosePopup: async () => {
                                    env.services.dialog.closeAll(); // Close all on cancel
                                }
                            }
                        }
                    );
                } else {
                    // Direct selection without PIN
                    env.services.dialog.closeAll();
                    await processEmployeeSelection(employeeId);
                }
            },
            onClosePopup: async () => {
                env.services.dialog.closeAll();
            }
        }
    }
);
```

### **3. Conditional Dialog Pattern**
```javascript
export async function MyClientAction(env, action) {
    try {
        // Check if dialog is needed
        const needsValidation = await env.services.orm.call("model", "check_validation", [id]);
        
        if (!needsValidation) {
            // No dialog needed, proceed directly
            await env.services.orm.call("model", "process_directly", [id]);
            env.services.action.doAction({ type: 'ir.actions.act_window_close' });
            return;
        }
        
        // Dialog needed
        env.services.dialog.add(DialogWrapper, {
            Component: ValidationPopup,
            componentProps: {
                onValidate: async (data) => {
                    await env.services.orm.call("model", "process_with_validation", [id], { context: data });
                    env.services.dialog.closeAll();
                    env.services.action.doAction({ type: 'ir.actions.act_window_close' });
                }
            }
        });
        
    } catch (error) {
        console.error("Error:", error);
        env.services.notification.add(_t("Error: ") + error.message, { type: "danger" });
    }
}
```

## 🎨 **Dialog Component Patterns**

### **1. DialogWrapper Usage**
```javascript
// Standard DialogWrapper pattern
env.services.dialog.add(
    DialogWrapper, // Always use DialogWrapper for consistency
    {
        Component: YourPopupComponent,
        componentProps: {
            // Props passed to your component
            popupData: { title: _t("Title"), data: myData },
            onClosePopup: async (popupId) => {
                env.services.dialog.closeAll();
            }
        }
    },
    {
        // Dialog options
        onClose: () => {
            console.log("Dialog closed");
        }
    }
);
```

### **2. Component Props Pattern**
```javascript
// Your popup component should expect these standard props
YourPopupComponent.props = {
    popupData: Object,           // Data for the popup
    onClosePopup: Function,      // Close callback
    onConfirm: Function,         // Confirmation callback (optional)
    // ... other specific props
};

// In your component
export class YourPopupComponent extends Component {
    async confirm() {
        const result = await this.processData();
        if (result) {
            await this.props.onConfirm(result);
        }
    }
    
    async cancel() {
        await this.props.onClosePopup('YourPopup');
    }
}
```

## 🔄 **Dialog State Management**

### **1. Loading States in Dialogs**
```javascript
// Component with loading state
export class ProcessingPopup extends Component {
    setup() {
        this.state = useState({
            isLoading: false,
            error: null
        });
    }
    
    async processAction() {
        this.state.isLoading = true;
        this.state.error = null;
        
        try {
            const result = await env.services.orm.call("model", "method", [params]);
            
            if (result.success) {
                env.services.notification.add(_t("Success"), { type: "success" });
                env.services.dialog.closeAll();
            } else {
                this.state.error = result.message;
            }
        } catch (error) {
            this.state.error = error.message;
        } finally {
            this.state.isLoading = false;
        }
    }
}
```

### **2. Dialog Data Validation**
```javascript
// Validation before dialog actions
async validateAndProcess(data) {
    // Client-side validation
    if (!data.requiredField) {
        env.services.notification.add(_t("Required field missing"), { type: "warning" });
        return false;
    }
    
    try {
        // Server-side validation
        const isValid = await env.services.orm.call("model", "validate_data", [data]);
        
        if (!isValid) {
            env.services.notification.add(_t("Validation failed"), { type: "danger" });
            return false;
        }
        
        // Process if valid
        await this.processValidData(data);
        env.services.dialog.closeAll();
        return true;
        
    } catch (error) {
        env.services.notification.add(_t("Validation error: ") + error.message, { type: "danger" });
        return false;
    }
}
```

## 🧪 **Testing Dialog Patterns**

### **1. Dialog Opening Test**
```javascript
// Test dialog creation
function testDialogCreation() {
    const dialogCount = document.querySelectorAll('.modal').length;
    
    // Trigger dialog
    env.services.dialog.add(DialogWrapper, { Component: TestPopup });
    
    // Verify dialog opened
    const newDialogCount = document.querySelectorAll('.modal').length;
    console.assert(newDialogCount === dialogCount + 1, "Dialog should open");
}
```

### **2. Dialog Closing Test**
```javascript
// Test dialog closing
function testDialogClosing() {
    // Open dialog
    env.services.dialog.add(DialogWrapper, { Component: TestPopup });
    
    // Close all dialogs
    env.services.dialog.closeAll();
    
    // Verify all dialogs closed
    setTimeout(() => {
        const remainingDialogs = document.querySelectorAll('.modal').length;
        console.assert(remainingDialogs === 0, "All dialogs should be closed");
    }, 100);
}
```

## 🎯 **Best Practices Summary**

### **✅ Do's**
- Always use `env.services.dialog.closeAll()` for closing dialogs
- Use DialogWrapper for consistent dialog structure
- Implement proper error handling in dialog actions
- Provide loading states for async operations
- Validate data before processing
- Use meaningful component props structure

### **❌ Don'ts**
- Don't rely on individual `dialog.close()` methods
- Don't forget to close dialogs after successful operations
- Don't mix business logic with dialog management
- Don't create dialogs without proper error handling
- Don't forget to handle dialog cancellation scenarios

### **🔧 Common Patterns**
1. **Check → Dialog → Validate → Process → Close**
2. **Dialog → Nested Dialog → Validate → Close All**
3. **Conditional Dialog → Direct Process or Dialog Flow**

## 📚 **Integration with Client Actions**

```javascript
// Complete pattern: Client Action + Dialog + Backend
export async function MyClientAction(env, action) {
    try {
        // 1. Check if dialog needed
        const needsDialog = await checkCondition();
        
        if (!needsDialog) {
            // 2a. Direct processing
            await processDirectly();
            env.services.action.doAction({ type: 'ir.actions.act_window_close' });
            return;
        }
        
        // 2b. Dialog processing
        env.services.dialog.add(DialogWrapper, {
            Component: MyPopup,
            componentProps: {
                onConfirm: async (data) => {
                    // 3. Process with dialog data
                    await processWithData(data);
                    
                    // 4. Close everything
                    env.services.dialog.closeAll();
                    env.services.action.doAction({ type: 'ir.actions.act_window_close' });
                },
                onClosePopup: () => {
                    env.services.dialog.closeAll();
                }
            }
        });
        
    } catch (error) {
        console.error("Error:", error);
        env.services.notification.add(_t("Error: ") + error.message, { type: "danger" });
    }
}
```

---

**Based on**: Employee selection popup with PIN validation implementation
**Key Learning**: `env.services.dialog.closeAll()` is the reliable way to close dialogs
**Status**: ✅ Production Ready
