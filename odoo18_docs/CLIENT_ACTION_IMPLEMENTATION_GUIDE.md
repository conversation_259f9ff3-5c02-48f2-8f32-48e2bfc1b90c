# Client Action Implementation Guide - Odoo 18

## 🎯 **Overview**

This guide provides proven patterns for implementing `ir.actions.client` actions in Odoo 18, based on successful implementation of the `action_validate_with_employee_selection` client action.

## 📋 **Client Action Architecture**

### **1. Backend Trigger Method**
```python
# wizard/sale_order_discount.py
def action_validate_with_employee_selection(self):
    """Method that triggers the client action"""
    # Calculate parameters
    if self.discount_type in ["so_discount", "sol_discount"]:
        discount_percentage = self.discount_percentage*100
    else:
        discount_percentage = (self.discount_amount * 100) / self.sale_order_id.amount_untaxed
        
    return {
        "type": "ir.actions.client",
        "tag": "action_validate_with_employee_selection",  # Must match registry key
        "params": {
            "model": "sale.order",
            "sale_order_id": self.sale_order_id.id,
            "discount_type": self.discount_type,
            "discount_percentage": discount_percentage,
            "discount_amount": self.discount_amount,
            "order_lines": self.sale_order_id.order_line.read(['id', 'product_id', 'discount']),
        },
    }
```

### **2. Frontend Client Action Handler**
```javascript
// static/src/employee_selection/employee_hooks.js
export async function ActionValidateWithEmployeeSelection(env, action) {
    console.log("action_validate_with_employee_selection triggered", action);
    
    try {
        // Access parameters from action.params
        const saleOrderId = action.params.sale_order_id;
        const discountType = action.params.discount_type;
        const orderLines = action.params.order_lines;
        
        // Use env.services for all service access
        const result = await env.services.orm.call("model", "method", [params]);
        
        // Handle success/error
        if (result) {
            env.services.notification.add(_t("Success message"), { type: "success" });
            env.services.action.doAction({ type: 'ir.actions.act_window_close' });
        }
        
    } catch (error) {
        console.error("Error in client action:", error);
        env.services.notification.add(_t("Error message"), { type: "danger" });
    }
}

// Register the client action
registry.category("actions").add("action_validate_with_employee_selection", ActionValidateWithEmployeeSelection);
```

### **3. XML Button Integration**
```xml
<!-- views/wizard_views.xml -->
<xpath expr="//button[@name='original_action']" position="attributes">
    <attribute name="invisible">1</attribute>
</xpath>
<xpath expr="//button[@name='original_action']" position="after">
    <button type="object" 
            string="Apply" 
            name="action_validate_with_employee_selection" 
            class="btn btn-primary" 
            data-hotkey="q"/>
</xpath>
```

## 🔧 **Key Implementation Patterns**

### **1. Service Access Pattern**
```javascript
// ✅ CORRECT: Use env.services directly in client actions
export async function MyClientAction(env, action) {
    // ORM calls
    const result = await env.services.orm.call("model", "method", [params]);
    
    // Notifications
    env.services.notification.add(_t("Message"), { type: "success" });
    
    // Actions
    env.services.action.doAction({ type: 'ir.actions.act_window_close' });
    
    // Dialog management
    env.services.dialog.add(Component, props);
    env.services.dialog.closeAll();
}

// ❌ WRONG: Don't use useService hooks in client actions
export function MyClientAction(env, action) {
    const orm = useService("orm"); // This will fail!
}
```

### **2. Parameter Passing Pattern**
```python
# Backend: Structure parameters clearly
return {
    "type": "ir.actions.client",
    "tag": "my_client_action",
    "params": {
        "model": self._name,
        "record_id": self.id,
        "context_data": {
            "key1": value1,
            "key2": value2,
        },
        "related_records": self.related_ids.read(['field1', 'field2']),
    },
}
```

```javascript
// Frontend: Access parameters systematically
export async function MyClientAction(env, action) {
    const recordId = action.params.record_id;
    const contextData = action.params.context_data;
    const relatedRecords = action.params.related_records;
    
    // Use parameters in logic
}
```

### **3. Error Handling Pattern**
```javascript
export async function MyClientAction(env, action) {
    try {
        // Main logic
        const result = await env.services.orm.call(...);
        
        if (result && result.success) {
            env.services.notification.add(_t("Operation completed successfully"), { type: "success" });
            env.services.action.doAction({ type: 'ir.actions.act_window_close' });
        } else {
            const errorMessage = result && result.message ? result.message : "Unknown error";
            env.services.notification.add(_t(errorMessage), { type: "danger" });
        }
        
    } catch (error) {
        console.error("Error in client action:", error);
        env.services.notification.add(_t("Error: ") + error.message, { type: "danger" });
    }
}
```

## 🎨 **Dialog Integration Patterns**

### **1. Dialog Creation and Management**
```javascript
// Create dialog
const dialog = env.services.dialog.add(
    DialogWrapper,
    {
        Component: MyPopupComponent,
        componentProps: {
            popupData: { title: _t("Title"), data: myData },
            onClosePopup: async (popupId) => {
                env.services.dialog.closeAll(); // ✅ Reliable closing
            },
            onConfirm: async (data) => {
                // Handle confirmation
                await processData(data);
                env.services.dialog.closeAll();
            }
        }
    }
);
```

### **2. Multiple Dialog Handling**
```javascript
// ✅ CORRECT: Use closeAll() for multiple dialogs
if (needsPinValidation) {
    env.services.dialog.add(PinPopup, {
        onPinValidate: async (pin) => {
            if (validatePin(pin)) {
                env.services.dialog.closeAll(); // Closes both PIN and parent dialogs
                await continueProcess();
            }
        }
    });
}
```

## 📚 **Template Method Integration**

### **1. Base Template Method**
```python
# Base module: modula_sale_employee_selection/models/sale_order.py
def action_employee_validation_for_sale_order(self):
    """Template method for employee validation workflow"""
    self.ensure_one()
    return True  # Base implementation
```

### **2. Business Logic Override**
```python
# Dependent module: modula_sale/models/sale_order.py
def action_employee_validation_for_sale_order(self):
    """Override: Apply discount after employee validation"""
    res = super().action_employee_validation_for_sale_order()
    
    # Extract context parameters
    context = self.env.context
    discount_type = context.get('discount_type')
    discount_percentage = context.get('discount_percentage')
    
    # Apply business logic
    wizard = self.env['sale.order.discount'].create({
        'sale_order_id': self.id,
        'discount_type': discount_type,
        'discount_percentage': discount_percentage/100,
    })
    wizard.action_apply_discount()
    
    return res
```

## 🧪 **Testing Patterns**

### **1. Client Action Testing**
```javascript
// Test client action registration
console.log("Testing client action registration:");
console.log(registry.category("actions").get("action_validate_with_employee_selection"));

// Test parameter passing
const testAction = {
    params: {
        sale_order_id: 1,
        discount_type: "amount",
        discount_amount: 100
    }
};
```

### **2. Backend Method Testing**
```python
# Test template method pattern
def test_employee_validation_method(self):
    order = self.env['sale.order'].create({...})
    
    # Test base implementation
    result = order.action_employee_validation_for_sale_order()
    self.assertTrue(result)
    
    # Test with context
    result = order.with_context(
        discount_type='amount',
        discount_amount=100
    ).action_employee_validation_for_sale_order()
    self.assertTrue(result)
```

## 🎯 **Best Practices Summary**

### **✅ Do's**
- Use `env.services` directly for all service access
- Structure parameters clearly in backend methods
- Use `env.services.dialog.closeAll()` for reliable dialog closing
- Implement proper error handling with try-catch blocks
- Use template method pattern for extensibility
- Register client actions with descriptive names

### **❌ Don'ts**
- Don't use `useService` hooks in client actions
- Don't rely on individual `dialog.close()` methods
- Don't forget error handling and user feedback
- Don't hardcode values - use parameters from action.params
- Don't mix business logic in client action handlers

---

**Based on**: Successful implementation of `action_validate_with_employee_selection` in modula_sale_employee_selection module
**Last Updated**: Current implementation
**Status**: ✅ Production Ready
