# -*- coding: utf-8 -*-

import logging

_logger = logging.getLogger(__name__)


def create_initial_url_mappings(env):
    """Create URL mappings for existing records
    
    This function creates URL mappings for all existing products, categories,
    blogs, and blog posts to ensure backward compatibility when the module
    is installed.
    
    Args:
        env: Odoo environment
    """
    try:
        _logger.info("Starting creation of initial URL mappings...")
        
        # Create product mappings
        product_count = _create_product_mappings(env)
        
        # Create category mappings
        category_count = _create_category_mappings(env)
        
        # Create blog mappings
        blog_count = _create_blog_mappings(env)
        
        # Create blog post mappings
        post_count = _create_blog_post_mappings(env)
        
        # Create custom page mappings
        custom_count = _create_custom_page_mappings(env)
        
        _logger.info(
            "URL mapping creation completed: %d products, %d categories, "
            "%d blogs, %d posts, %d custom pages",
            product_count, category_count, blog_count, post_count, custom_count
        )
        
        # Commit the transaction
        env.cr.commit()
        
    except Exception as e:
        _logger.error("Error creating initial URL mappings: %s", str(e))
        env.cr.rollback()
        raise


def _create_product_mappings(env):
    """Create URL mappings for products
    
    Args:
        env: Odoo environment
        
    Returns:
        int: Number of mappings created
    """
    try:
        _logger.info("Creating product URL mappings...")
        
        products = env['product.template'].search([('website_published', '=', True)])
        created_count = 0
        
        for product in products:
            try:
                # Generate clean URL
                clean_name = env['ir.http']._slugify_clean(product.name)
                if not clean_name:
                    continue
                
                new_url = f"/{clean_name}"
                old_url = f"/shop/{env['ir.http']._slug(product)}"
                
                # Skip if URLs are the same
                if old_url == new_url:
                    continue
                
                # Check if mapping already exists
                existing = env['url.mapping'].search([
                    ('old_url', '=', old_url),
                    ('model_name', '=', 'product.template'),
                    ('record_id', '=', product.id)
                ], limit=1)
                
                if not existing:
                    env['url.mapping'].create({
                        'old_url': old_url,
                        'new_url': new_url,
                        'model_name': 'product.template',
                        'record_id': product.id,
                        'redirect_type': '301',
                    })
                    created_count += 1
                
            except Exception as e:
                _logger.warning("Error creating mapping for product %s: %s", 
                               product.id, str(e))
        
        _logger.info("Created %d product URL mappings", created_count)
        return created_count
        
    except Exception as e:
        _logger.error("Error creating product mappings: %s", str(e))
        return 0


def _create_category_mappings(env):
    """Create URL mappings for categories
    
    Args:
        env: Odoo environment
        
    Returns:
        int: Number of mappings created
    """
    try:
        _logger.info("Creating category URL mappings...")
        
        categories = env['product.public.category'].search([])
        created_count = 0
        
        for category in categories:
            try:
                # Generate clean URL
                if category.parent_id:
                    parent_slug = env['ir.http']._slugify_clean(category.parent_id.name)
                    child_slug = env['ir.http']._slugify_clean(category.name)
                    if parent_slug and child_slug:
                        new_url = f"/{parent_slug}/{child_slug}"
                    else:
                        continue
                else:
                    clean_name = env['ir.http']._slugify_clean(category.name)
                    if clean_name:
                        new_url = f"/{clean_name}"
                    else:
                        continue
                
                old_url = f"/shop/category/{env['ir.http']._slug(category)}"
                
                # Skip if URLs are the same
                if old_url == new_url:
                    continue
                
                # Check if mapping already exists
                existing = env['url.mapping'].search([
                    ('old_url', '=', old_url),
                    ('model_name', '=', 'product.public.category'),
                    ('record_id', '=', category.id)
                ], limit=1)
                
                if not existing:
                    env['url.mapping'].create({
                        'old_url': old_url,
                        'new_url': new_url,
                        'model_name': 'product.public.category',
                        'record_id': category.id,
                        'redirect_type': '301',
                    })
                    created_count += 1
                
            except Exception as e:
                _logger.warning("Error creating mapping for category %s: %s", 
                               category.id, str(e))
        
        _logger.info("Created %d category URL mappings", created_count)
        return created_count
        
    except Exception as e:
        _logger.error("Error creating category mappings: %s", str(e))
        return 0


def _create_blog_mappings(env):
    """Create URL mappings for blogs
    
    Args:
        env: Odoo environment
        
    Returns:
        int: Number of mappings created
    """
    try:
        _logger.info("Creating blog URL mappings...")
        
        blogs = env['blog.blog'].search([])
        created_count = 0
        
        for blog in blogs:
            try:
                # Generate clean URL
                clean_name = env['ir.http']._slugify_clean(blog.name)
                if not clean_name:
                    continue
                
                new_url = f"/blog/{clean_name}"
                old_url = f"/blog/{env['ir.http']._slug(blog)}"
                
                # Skip if URLs are the same
                if old_url == new_url:
                    continue
                
                # Check if mapping already exists
                existing = env['url.mapping'].search([
                    ('old_url', '=', old_url),
                    ('model_name', '=', 'blog.blog'),
                    ('record_id', '=', blog.id)
                ], limit=1)
                
                if not existing:
                    env['url.mapping'].create({
                        'old_url': old_url,
                        'new_url': new_url,
                        'model_name': 'blog.blog',
                        'record_id': blog.id,
                        'redirect_type': '301',
                    })
                    created_count += 1
                
            except Exception as e:
                _logger.warning("Error creating mapping for blog %s: %s", 
                               blog.id, str(e))
        
        _logger.info("Created %d blog URL mappings", created_count)
        return created_count
        
    except Exception as e:
        _logger.error("Error creating blog mappings: %s", str(e))
        return 0


def _create_blog_post_mappings(env):
    """Create URL mappings for blog posts
    
    Args:
        env: Odoo environment
        
    Returns:
        int: Number of mappings created
    """
    try:
        _logger.info("Creating blog post URL mappings...")
        
        posts = env['blog.post'].search([('website_published', '=', True)])
        created_count = 0
        
        for post in posts:
            try:
                if not post.blog_id:
                    continue
                
                # Generate clean URL
                blog_slug = env['ir.http']._slugify_clean(post.blog_id.name)
                post_slug = env['ir.http']._slugify_clean(post.name)
                
                if not blog_slug or not post_slug:
                    continue
                
                new_url = f"/blog/{blog_slug}/{post_slug}"
                old_url = f"/blog/{env['ir.http']._slug(post.blog_id)}/{env['ir.http']._slug(post)}"
                
                # Skip if URLs are the same
                if old_url == new_url:
                    continue
                
                # Check if mapping already exists
                existing = env['url.mapping'].search([
                    ('old_url', '=', old_url),
                    ('model_name', '=', 'blog.post'),
                    ('record_id', '=', post.id)
                ], limit=1)
                
                if not existing:
                    env['url.mapping'].create({
                        'old_url': old_url,
                        'new_url': new_url,
                        'model_name': 'blog.post',
                        'record_id': post.id,
                        'redirect_type': '301',
                    })
                    created_count += 1
                
            except Exception as e:
                _logger.warning("Error creating mapping for blog post %s: %s", 
                               post.id, str(e))
        
        _logger.info("Created %d blog post URL mappings", created_count)
        return created_count
        
    except Exception as e:
        _logger.error("Error creating blog post mappings: %s", str(e))
        return 0


def _create_custom_page_mappings(env):
    """Create URL mappings for custom pages
    
    Args:
        env: Odoo environment
        
    Returns:
        int: Number of mappings created
    """
    try:
        _logger.info("Creating custom page URL mappings...")
        
        # Custom page mappings from controller
        custom_pages = {
            'warranty-claim-form': '/helpdesk/warranty-claim-2',
            'contact-us': '/contactus',
            'our-story': '/our-story',
            'showroom': '/showroom',
            'terms-and-conditions': '/terms',
            'privacy-policy': '/privacy',
            'care-and-maintenance': '/care-and-maintenance',
            'shipping-and-delivery': '/shipping-and-delivery',
            'returns-and-refunds': '/returns-and-refunds',
        }
        
        created_count = 0
        
        for clean_url, old_url in custom_pages.items():
            try:
                new_url = f"/{clean_url}"
                
                # Check if mapping already exists
                existing = env['url.mapping'].search([
                    ('old_url', '=', old_url),
                    ('new_url', '=', new_url)
                ], limit=1)
                
                if not existing:
                    env['url.mapping'].create({
                        'old_url': old_url,
                        'new_url': new_url,
                        'model_name': 'website.page',  # Generic model for custom pages
                        'record_id': 0,  # No specific record ID for custom pages
                        'redirect_type': '301',
                    })
                    created_count += 1
                
            except Exception as e:
                _logger.warning("Error creating mapping for custom page %s: %s", 
                               clean_url, str(e))
        
        _logger.info("Created %d custom page URL mappings", created_count)
        return created_count
        
    except Exception as e:
        _logger.error("Error creating custom page mappings: %s", str(e))
        return 0


def regenerate_all_url_mappings(env):
    """Regenerate all URL mappings (maintenance function)
    
    Args:
        env: Odoo environment
        
    Returns:
        dict: Results of regeneration
    """
    try:
        _logger.info("Starting regeneration of all URL mappings...")
        
        # Deactivate all existing mappings
        env['url.mapping'].search([]).write({'active': False})
        
        # Recreate all mappings
        results = {
            'products': _create_product_mappings(env),
            'categories': _create_category_mappings(env),
            'blogs': _create_blog_mappings(env),
            'posts': _create_blog_post_mappings(env),
            'custom_pages': _create_custom_page_mappings(env),
        }
        
        # Clean up invalid mappings
        cleanup_count = env['url.mapping'].cleanup_invalid_mappings()
        results['cleaned_invalid'] = cleanup_count
        
        _logger.info("URL mapping regeneration completed: %s", results)
        
        env.cr.commit()
        return results
        
    except Exception as e:
        _logger.error("Error regenerating URL mappings: %s", str(e))
        env.cr.rollback()
        return {'error': str(e)}
