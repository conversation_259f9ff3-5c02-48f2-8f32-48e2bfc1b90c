<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    
    <!-- Demo URL Mappings for Products -->
    <record id="demo_url_mapping_product_1" model="url.mapping">
        <field name="old_url">/shop/demo-sofa-modular-123</field>
        <field name="new_url">/demo-sofa-modular</field>
        <field name="model_name">product.template</field>
        <field name="record_id">1</field>
        <field name="redirect_type">301</field>
    </record>

    <record id="demo_url_mapping_product_2" model="url.mapping">
        <field name="old_url">/shop/demo-chair-swivel-456</field>
        <field name="new_url">/demo-chair-swivel</field>
        <field name="model_name">product.template</field>
        <field name="record_id">2</field>
        <field name="redirect_type">301</field>
    </record>

    <!-- Demo URL Mappings for Categories -->
    <record id="demo_url_mapping_category_1" model="url.mapping">
        <field name="old_url">/shop/category/sofas-modular-sofas-31</field>
        <field name="new_url">/sofas/modular-sofas</field>
        <field name="model_name">product.public.category</field>
        <field name="record_id">1</field>
        <field name="redirect_type">301</field>
    </record>

    <record id="demo_url_mapping_category_2" model="url.mapping">
        <field name="old_url">/shop/category/chairs-swivel-chairs-36</field>
        <field name="new_url">/chairs/swivel-chairs</field>
        <field name="model_name">product.public.category</field>
        <field name="record_id">2</field>
        <field name="redirect_type">301</field>
    </record>

    <!-- Demo URL Mappings for Blogs -->
    <record id="demo_url_mapping_blog_1" model="url.mapping">
        <field name="old_url">/blog/modula-living-journal-1</field>
        <field name="new_url">/blog/modula-living-journal</field>
        <field name="model_name">blog.blog</field>
        <field name="record_id">1</field>
        <field name="redirect_type">301</field>
    </record>

    <!-- Demo URL Mappings for Blog Posts -->
    <record id="demo_url_mapping_post_1" model="url.mapping">
        <field name="old_url">/blog/modula-living-journal-1/our-grand-opening-1</field>
        <field name="new_url">/blog/modula-living-journal/our-grand-opening</field>
        <field name="model_name">blog.post</field>
        <field name="record_id">1</field>
        <field name="redirect_type">301</field>
    </record>

    <!-- Demo URL Mappings for Custom Pages -->
    <record id="demo_url_mapping_warranty" model="url.mapping">
        <field name="old_url">/helpdesk/warranty-claim-2</field>
        <field name="new_url">/warranty-claim-form</field>
        <field name="model_name">website.page</field>
        <field name="record_id">0</field>
        <field name="redirect_type">301</field>
    </record>

    <record id="demo_url_mapping_contact" model="url.mapping">
        <field name="old_url">/contactus</field>
        <field name="new_url">/contact-us</field>
        <field name="model_name">website.page</field>
        <field name="record_id">0</field>
        <field name="redirect_type">301</field>
    </record>

    <record id="demo_url_mapping_terms" model="url.mapping">
        <field name="old_url">/terms</field>
        <field name="new_url">/terms-and-conditions</field>
        <field name="model_name">website.page</field>
        <field name="record_id">0</field>
        <field name="redirect_type">301</field>
    </record>

    <record id="demo_url_mapping_privacy" model="url.mapping">
        <field name="old_url">/privacy</field>
        <field name="new_url">/privacy-policy</field>
        <field name="model_name">website.page</field>
        <field name="record_id">0</field>
        <field name="redirect_type">301</field>
    </record>

</odoo>
