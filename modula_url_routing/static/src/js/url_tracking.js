/** @odoo-module **/

import { Component, onMounted, onWillUnmount } from "@odoo/owl";

/**
 * URL Tracking Component for Frontend
 * 
 * This component tracks URL changes and provides analytics
 * for the clean URL routing system.
 */
export class UrlTracker extends Component {
    setup() {
        this.originalUrl = window.location.href;
        this.startTime = Date.now();
        
        onMounted(() => {
            this.trackPageView();
            this.setupUrlChangeTracking();
        });
        
        onWillUnmount(() => {
            this.trackPageExit();
        });
    }
    
    /**
     * Track page view for analytics
     */
    trackPageView() {
        try {
            // Check if this is a clean URL
            const isCleanUrl = this.isCleanUrl(window.location.pathname);
            
            if (isCleanUrl) {
                console.log('Clean URL accessed:', window.location.pathname);
                
                // Send analytics data if available
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'page_view', {
                        page_title: document.title,
                        page_location: window.location.href,
                        custom_parameters: {
                            url_type: 'clean'
                        }
                    });
                }
            }
        } catch (error) {
            console.warn('URL tracking error:', error);
        }
    }
    
    /**
     * Track page exit
     */
    trackPageExit() {
        try {
            const timeOnPage = Date.now() - this.startTime;
            
            if (this.isCleanUrl(window.location.pathname)) {
                console.log('Clean URL exit:', window.location.pathname, 'Time:', timeOnPage + 'ms');
            }
        } catch (error) {
            console.warn('URL exit tracking error:', error);
        }
    }
    
    /**
     * Setup URL change tracking for SPA navigation
     */
    setupUrlChangeTracking() {
        // Track browser back/forward navigation
        window.addEventListener('popstate', (event) => {
            this.trackUrlChange(window.location.href);
        });
        
        // Track programmatic navigation
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        history.pushState = function(...args) {
            originalPushState.apply(history, args);
            this.trackUrlChange(window.location.href);
        }.bind(this);
        
        history.replaceState = function(...args) {
            originalReplaceState.apply(history, args);
            this.trackUrlChange(window.location.href);
        }.bind(this);
    }
    
    /**
     * Track URL changes
     */
    trackUrlChange(newUrl) {
        try {
            const newPath = new URL(newUrl).pathname;
            
            if (this.isCleanUrl(newPath)) {
                console.log('Clean URL navigation:', newPath);
                
                // Send navigation event
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'page_view', {
                        page_title: document.title,
                        page_location: newUrl,
                        custom_parameters: {
                            url_type: 'clean',
                            navigation_type: 'spa'
                        }
                    });
                }
            }
        } catch (error) {
            console.warn('URL change tracking error:', error);
        }
    }
    
    /**
     * Check if URL is a clean URL (without IDs)
     */
    isCleanUrl(pathname) {
        // Clean URLs don't have ID patterns like -123 at the end
        const hasIdPattern = /-\d+$/.test(pathname.split('/').pop());
        
        // Clean URLs for products, categories, blogs
        const isProductUrl = /^\/[a-z0-9-]+$/.test(pathname) && !hasIdPattern;
        const isCategoryUrl = /^\/[a-z0-9-]+\/[a-z0-9-]+$/.test(pathname) && !hasIdPattern;
        const isBlogUrl = /^\/blog\/[a-z0-9-]+/.test(pathname) && !hasIdPattern;
        const isCustomPageUrl = /^\/[a-z0-9-]+-[a-z0-9-]+$/.test(pathname) && pathname.includes('-');
        
        return isProductUrl || isCategoryUrl || isBlogUrl || isCustomPageUrl;
    }
}

// Auto-initialize URL tracking when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    try {
        // Only initialize on frontend pages
        if (document.body.classList.contains('frontend')) {
            const tracker = new UrlTracker();
            window.urlTracker = tracker;
        }
    } catch (error) {
        console.warn('URL tracker initialization failed:', error);
    }
});

// Export for manual initialization if needed
export default UrlTracker;
