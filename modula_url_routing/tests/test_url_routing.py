# -*- coding: utf-8 -*-

from odoo.tests import TransactionCase, tagged
import logging

_logger = logging.getLogger(__name__)


@tagged('url_routing')
class TestUrlRouting(TransactionCase):
    
    def setUp(self):
        super().setUp()
        
        # Create test product
        self.test_product = self.env['product.template'].create({
            'name': 'Test Sofa Modular',
            'website_published': True,
            'type': 'product',
        })
        
        # Create test category
        self.test_parent_category = self.env['product.public.category'].create({
            'name': 'Test Sofas',
        })
        self.test_child_category = self.env['product.public.category'].create({
            'name': 'Test Modular',
            'parent_id': self.test_parent_category.id,
        })
        
        # Create test blog
        self.test_blog = self.env['blog.blog'].create({
            'name': 'Test Living Journal',
        })
        
        # Create test blog post
        self.test_post = self.env['blog.post'].create({
            'name': 'Test Grand Opening',
            'blog_id': self.test_blog.id,
            'website_published': True,
        })
    
    def test_clean_slug_generation(self):
        """Test clean slug generation without IDs"""
        # Test basic slug generation
        clean_slug = self.env['ir.http']._slugify_clean('Test Sofa Modular')
        self.assertEqual(clean_slug, 'test-sofa-modular')
        
        # Test with special characters
        clean_slug = self.env['ir.http']._slugify_clean('Test & Special! Characters@')
        self.assertEqual(clean_slug, 'test-special-characters')
        
        # Test with multiple spaces
        clean_slug = self.env['ir.http']._slugify_clean('Test   Multiple    Spaces')
        self.assertEqual(clean_slug, 'test-multiple-spaces')
        
        # Test empty string
        clean_slug = self.env['ir.http']._slugify_clean('')
        self.assertEqual(clean_slug, '')
    
    def test_product_clean_url_generation(self):
        """Test product clean URL generation"""
        self.test_product._compute_clean_url()
        self.assertEqual(self.test_product.clean_url, '/test-sofa-modular')
        
        # Test website URL override
        self.test_product._compute_website_url()
        self.assertEqual(self.test_product.website_url, '/test-sofa-modular')
    
    def test_category_clean_url_generation(self):
        """Test category clean URL generation"""
        # Test child category URL
        self.test_child_category._compute_clean_url()
        self.assertEqual(self.test_child_category.clean_url, '/test-sofas/test-modular')
        
        # Test parent category URL
        self.test_parent_category._compute_clean_url()
        self.assertEqual(self.test_parent_category.clean_url, '/test-sofas')
    
    def test_blog_clean_url_generation(self):
        """Test blog clean URL generation"""
        self.test_blog._compute_clean_url()
        self.assertEqual(self.test_blog.clean_url, '/blog/test-living-journal')
    
    def test_blog_post_clean_url_generation(self):
        """Test blog post clean URL generation"""
        self.test_post._compute_clean_url()
        self.assertEqual(self.test_post.clean_url, '/blog/test-living-journal/test-grand-opening')
    
    def test_url_mapping_creation(self):
        """Test URL mapping creation"""
        # Force URL computation to trigger mapping creation
        self.test_product._compute_website_url()
        
        # Check if mapping was created
        mapping = self.env['url.mapping'].search([
            ('model_name', '=', 'product.template'),
            ('record_id', '=', self.test_product.id)
        ], limit=1)
        
        self.assertTrue(mapping, "URL mapping should be created for product")
        self.assertEqual(mapping.new_url, '/test-sofa-modular')
        self.assertTrue(mapping.old_url.startswith('/shop/'))
    
    def test_url_mapping_search_methods(self):
        """Test URL mapping search methods"""
        # Create a test mapping
        mapping = self.env['url.mapping'].create({
            'old_url': '/shop/test-product-123',
            'new_url': '/test-product',
            'model_name': 'product.template',
            'record_id': self.test_product.id,
        })
        
        # Test find by old URL
        found_mapping = self.env['url.mapping'].find_by_old_url('/shop/test-product-123')
        self.assertEqual(found_mapping.id, mapping.id)
        
        # Test find by new URL
        found_mapping = self.env['url.mapping'].find_by_new_url('/test-product')
        self.assertEqual(found_mapping.id, mapping.id)
        
        # Test not found
        not_found = self.env['url.mapping'].find_by_old_url('/nonexistent')
        self.assertFalse(not_found)
    
    def test_record_finding_methods(self):
        """Test record finding by clean URLs"""
        # Test product finding
        found_product = self.env['ir.http']._find_product_by_clean_name('test-sofa-modular')
        self.assertEqual(found_product.id, self.test_product.id)
        
        # Test category finding
        found_category = self.env['ir.http']._find_category_by_url_parts('test-sofas', 'test-modular')
        self.assertEqual(found_category.id, self.test_child_category.id)
        
        # Test blog finding
        found_blog = self.env['ir.http']._find_blog_by_clean_name('test-living-journal')
        self.assertEqual(found_blog.id, self.test_blog.id)
        
        # Test blog post finding
        found_post = self.env['ir.http']._find_post_by_clean_name(self.test_blog, 'test-grand-opening')
        self.assertEqual(found_post.id, self.test_post.id)
    
    def test_url_mapping_constraints(self):
        """Test URL mapping constraints"""
        # Create first mapping
        mapping1 = self.env['url.mapping'].create({
            'old_url': '/shop/test-1',
            'new_url': '/test-unique',
            'model_name': 'product.template',
            'record_id': 1,
        })
        
        # Try to create duplicate new URL (should fail)
        with self.assertRaises(Exception):
            self.env['url.mapping'].create({
                'old_url': '/shop/test-2',
                'new_url': '/test-unique',  # Duplicate new URL
                'model_name': 'product.template',
                'record_id': 2,
            })
    
    def test_url_mapping_cleanup(self):
        """Test URL mapping cleanup functionality"""
        # Create mapping for non-existent record
        invalid_mapping = self.env['url.mapping'].create({
            'old_url': '/shop/nonexistent-999999',
            'new_url': '/nonexistent',
            'model_name': 'product.template',
            'record_id': 999999,  # Non-existent ID
        })
        
        # Run cleanup
        cleaned_count = self.env['url.mapping'].cleanup_invalid_mappings()
        
        # Check that invalid mapping was deactivated
        invalid_mapping.refresh()
        self.assertFalse(invalid_mapping.active)
        self.assertGreaterEqual(cleaned_count, 1)
    
    def test_regenerate_clean_urls(self):
        """Test regeneration of clean URLs"""
        # Test product URL regeneration
        result = self.env['product.template'].regenerate_all_clean_urls()
        self.assertIn('total_products', result)
        self.assertIn('updated_count', result)
        self.assertIn('error_count', result)
        
        # Test category URL regeneration
        result = self.env['product.public.category'].regenerate_all_clean_urls()
        self.assertIn('total_categories', result)
        
        # Test blog URL regeneration
        result = self.env['blog.blog'].regenerate_all_clean_urls()
        self.assertIn('total_blogs', result)
        
        # Test blog post URL regeneration
        result = self.env['blog.post'].regenerate_all_clean_urls()
        self.assertIn('total_posts', result)
