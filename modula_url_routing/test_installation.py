#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple installation test script for modula_url_routing module

This script can be run to test basic functionality after module installation.
"""

import sys
import os

def test_module_installation():
    """Test basic module installation and functionality"""
    
    print("🚀 Testing modula_url_routing module installation...")
    print("=" * 60)
    
    try:
        # Test 1: Check if module files exist
        print("✓ Test 1: Checking module files...")
        
        required_files = [
            '__manifest__.py',
            '__init__.py',
            'models/__init__.py',
            'models/url_mapping.py',
            'models/ir_http.py',
            'controllers/__init__.py',
            'controllers/product_routing.py',
            'controllers/category_routing.py',
            'security/ir.model.access.csv',
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                print(f"❌ Missing file: {file_path}")
                return False
            else:
                print(f"   ✓ {file_path}")
        
        print("✓ All required files present")
        print()
        
        # Test 2: Check manifest syntax
        print("✓ Test 2: Checking manifest syntax...")
        
        try:
            with open('__manifest__.py', 'r') as f:
                manifest_content = f.read()
            
            # Basic syntax check
            compile(manifest_content, '__manifest__.py', 'exec')
            print("   ✓ Manifest syntax is valid")
        except SyntaxError as e:
            print(f"   ❌ Manifest syntax error: {e}")
            return False
        
        print()
        
        # Test 3: Check Python syntax for all Python files
        print("✓ Test 3: Checking Python syntax...")
        
        python_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r') as f:
                    content = f.read()
                compile(content, py_file, 'exec')
                print(f"   ✓ {py_file}")
            except SyntaxError as e:
                print(f"   ❌ Syntax error in {py_file}: {e}")
                return False
        
        print("✓ All Python files have valid syntax")
        print()
        
        # Test 4: Check XML syntax
        print("✓ Test 4: Checking XML syntax...")
        
        xml_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.xml'):
                    xml_files.append(os.path.join(root, file))
        
        if xml_files:
            try:
                import xml.etree.ElementTree as ET
                for xml_file in xml_files:
                    try:
                        ET.parse(xml_file)
                        print(f"   ✓ {xml_file}")
                    except ET.ParseError as e:
                        print(f"   ❌ XML syntax error in {xml_file}: {e}")
                        return False
                
                print("✓ All XML files have valid syntax")
            except ImportError:
                print("   ⚠ XML parsing not available, skipping XML syntax check")
        else:
            print("   ℹ No XML files found")
        
        print()
        
        # Test 5: Check dependencies
        print("✓ Test 5: Checking dependencies...")
        
        try:
            exec(open('__manifest__.py').read())
            manifest = locals()
            
            required_deps = ['website', 'website_sale', 'website_blog', 'http_routing']
            manifest_deps = manifest.get('depends', [])
            
            for dep in required_deps:
                if dep in manifest_deps:
                    print(f"   ✓ {dep}")
                else:
                    print(f"   ❌ Missing dependency: {dep}")
                    return False
            
            print("✓ All required dependencies present")
        except Exception as e:
            print(f"   ❌ Error checking dependencies: {e}")
            return False
        
        print()
        
        # Test 6: Check security file
        print("✓ Test 6: Checking security configuration...")
        
        try:
            with open('security/ir.model.access.csv', 'r') as f:
                lines = f.readlines()
            
            if len(lines) < 2:  # Header + at least one data line
                print("   ❌ Security file appears empty")
                return False
            
            # Check header
            header = lines[0].strip()
            expected_header = 'id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink'
            if header != expected_header:
                print(f"   ❌ Invalid security file header")
                return False
            
            print(f"   ✓ Security file has {len(lines)-1} access rules")
            print("✓ Security configuration is valid")
        except Exception as e:
            print(f"   ❌ Error checking security file: {e}")
            return False
        
        print()
        
        # Summary
        print("🎉 SUCCESS: Module installation test completed successfully!")
        print("=" * 60)
        print()
        print("Next steps:")
        print("1. Install the module in your Odoo instance:")
        print("   ./odoo/odoo-bin -c .vscode/modula.conf -i modula_url_routing -d modula18_dev --test-enable --stop-after-init")
        print()
        print("2. Test URL routing functionality:")
        print("   - Check that clean URLs are generated for products/categories")
        print("   - Test URL redirects work correctly")
        print("   - Verify URL mappings are created")
        print()
        print("3. Monitor logs for any errors or warnings")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error during testing: {e}")
        return False

def main():
    """Main function"""
    
    # Change to module directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Run tests
    success = test_module_installation()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
