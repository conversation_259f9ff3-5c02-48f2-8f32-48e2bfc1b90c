# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import werkzeug
import logging

_logger = logging.getLogger(__name__)


class RedirectController(http.Controller):
    """
    Fallback controller to handle old URLs and provide redirects
    to new clean URLs when appropriate.
    """
    
    @http.route('/shop/<path:old_path>', type='http', auth="public", website=True, sitemap=False)
    def handle_old_shop_urls(self, old_path, **kwargs):
        """Handle redirects from old shop URLs to new clean URLs
        
        This controller provides backward compatibility by redirecting
        old ID-based URLs to new clean URLs when mappings exist.
        
        Args:
            old_path (str): Old URL path after /shop/
            **kwargs: Query parameters
            
        Returns:
            werkzeug.Response: Redirect to clean URL or pass through to original controller
        """
        try:
            old_url = f"/shop/{old_path}"
            
            # Check if we have a mapping to a clean URL
            mapping = request.env['url.mapping'].find_by_old_url(old_url)
            
            if mapping and mapping.record_exists:
                # Redirect to clean URL
                new_url = mapping.new_url
                
                # Preserve query parameters
                if kwargs:
                    query_string = werkzeug.urls.url_encode(kwargs)
                    new_url += f"?{query_string}"
                
                _logger.debug("Redirecting old shop URL %s to clean URL %s", 
                             old_url, new_url)
                
                # Use 301 redirect for SEO
                return request.redirect(new_url, code=301)
            
            # No mapping found, let the original controller handle it
            # This ensures existing functionality is preserved
            raise werkzeug.exceptions.NotFound()
            
        except werkzeug.exceptions.NotFound:
            # Let the original shop controller handle this URL
            raise
        except Exception as e:
            _logger.error("Error handling old shop URL %s: %s", old_url, str(e))
            # Let the original controller handle it
            raise werkzeug.exceptions.NotFound()
    
    @http.route('/blog/<path:old_path>', type='http', auth="public", website=True, sitemap=False)
    def handle_old_blog_urls(self, old_path, **kwargs):
        """Handle redirects from old blog URLs to new clean URLs
        
        Args:
            old_path (str): Old URL path after /blog/
            **kwargs: Query parameters
            
        Returns:
            werkzeug.Response: Redirect to clean URL or pass through to original controller
        """
        try:
            old_url = f"/blog/{old_path}"
            
            # Check if we have a mapping to a clean URL
            mapping = request.env['url.mapping'].find_by_old_url(old_url)
            
            if mapping and mapping.record_exists:
                # Redirect to clean URL
                new_url = mapping.new_url
                
                # Preserve query parameters
                if kwargs:
                    query_string = werkzeug.urls.url_encode(kwargs)
                    new_url += f"?{query_string}"
                
                _logger.debug("Redirecting old blog URL %s to clean URL %s", 
                             old_url, new_url)
                
                return request.redirect(new_url, code=301)
            
            # No mapping found, let the original controller handle it
            raise werkzeug.exceptions.NotFound()
            
        except werkzeug.exceptions.NotFound:
            raise
        except Exception as e:
            _logger.error("Error handling old blog URL %s: %s", old_url, str(e))
            raise werkzeug.exceptions.NotFound()
    
    @http.route('/helpdesk/<path:old_path>', type='http', auth="public", website=True, sitemap=False)
    def handle_old_helpdesk_urls(self, old_path, **kwargs):
        """Handle redirects from old helpdesk URLs to new clean URLs
        
        Args:
            old_path (str): Old URL path after /helpdesk/
            **kwargs: Query parameters
            
        Returns:
            werkzeug.Response: Redirect to clean URL or pass through to original controller
        """
        try:
            old_url = f"/helpdesk/{old_path}"
            
            # Check if we have a mapping to a clean URL
            mapping = request.env['url.mapping'].find_by_old_url(old_url)
            
            if mapping and mapping.record_exists:
                # Redirect to clean URL
                new_url = mapping.new_url
                
                # Preserve query parameters
                if kwargs:
                    query_string = werkzeug.urls.url_encode(kwargs)
                    new_url += f"?{query_string}"
                
                _logger.debug("Redirecting old helpdesk URL %s to clean URL %s", 
                             old_url, new_url)
                
                return request.redirect(new_url, code=301)
            
            # No mapping found, let the original controller handle it
            raise werkzeug.exceptions.NotFound()
            
        except werkzeug.exceptions.NotFound:
            raise
        except Exception as e:
            _logger.error("Error handling old helpdesk URL %s: %s", old_url, str(e))
            raise werkzeug.exceptions.NotFound()
    
    @http.route('/url_mapping/health_check', type='http', auth="public", website=True, sitemap=False)
    def url_mapping_health_check(self, **kwargs):
        """Health check endpoint for URL mapping functionality
        
        Returns:
            werkzeug.Response: JSON response with health status
        """
        try:
            # Check if URL mapping model is accessible
            mapping_count = request.env['url.mapping'].search_count([('active', '=', True)])
            
            # Check if core methods are working
            test_slug = request.env['ir.http']._slugify_clean("Test Product Name")
            
            health_data = {
                'status': 'healthy',
                'active_mappings': mapping_count,
                'test_slug': test_slug,
                'timestamp': request.env['ir.http']._get_current_timestamp() if hasattr(request.env['ir.http'], '_get_current_timestamp') else 'N/A'
            }
            
            return request.make_json_response(health_data)
            
        except Exception as e:
            _logger.error("URL mapping health check failed: %s", str(e))
            return request.make_json_response({
                'status': 'unhealthy',
                'error': str(e)
            }, status=500)
    
    @http.route('/url_mapping/cleanup', type='http', auth="user", website=True, sitemap=False)
    def url_mapping_cleanup(self, **kwargs):
        """Cleanup endpoint for invalid URL mappings (admin only)
        
        Returns:
            werkzeug.Response: JSON response with cleanup results
        """
        try:
            # Check if user has admin rights
            if not request.env.user.has_group('base.group_system'):
                return request.make_json_response({
                    'error': 'Access denied'
                }, status=403)
            
            # Run cleanup
            cleaned_count = request.env['url.mapping'].cleanup_invalid_mappings()
            
            return request.make_json_response({
                'status': 'success',
                'cleaned_mappings': cleaned_count,
                'message': f'Cleaned up {cleaned_count} invalid URL mappings'
            })
            
        except Exception as e:
            _logger.error("URL mapping cleanup failed: %s", str(e))
            return request.make_json_response({
                'status': 'error',
                'error': str(e)
            }, status=500)
