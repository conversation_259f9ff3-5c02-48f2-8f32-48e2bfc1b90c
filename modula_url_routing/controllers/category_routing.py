# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
from odoo.addons.website_sale.controllers.main import WebsiteSale
import werkzeug
import logging

_logger = logging.getLogger(__name__)


class CategoryRouting(http.Controller):
    
    @http.route([
        '/<string:parent_category>/<string:child_category>',
        '/<string:parent_category>/<string:child_category>/page/<int:page>',
    ], type='http', auth="public", website=True, sitemap=True)
    def category_clean_url(self, parent_category, child_category, page=1, **kwargs):
        """Handle clean category URLs like /sofas/modular-sofas
        
        This controller intercepts clean category URLs and redirects to the 
        existing shop controller, preserving all functionality while showing
        clean URLs in the browser.
        
        Args:
            parent_category (str): Parent category slug
            child_category (str): Child category slug  
            page (int): Page number for pagination
            **kwargs: Additional query parameters
            
        Returns:
            werkzeug.Response: Redirect to existing shop controller
        """
        try:
            # Find category by URL structure
            category = request.env['ir.http']._find_category_by_url_parts(
                parent_category, child_category
            )
            
            if not category:
                # Check URL mapping for redirects
                clean_url = f"/{parent_category}/{child_category}"
                mapping = request.env['url.mapping'].find_by_new_url(clean_url)
                if mapping and mapping.record_exists:
                    # Redirect to old URL if mapping exists
                    old_url = mapping.old_url
                    if page > 1:
                        old_url += f"/page/{page}"
                    if kwargs:
                        query_string = werkzeug.urls.url_encode(kwargs)
                        old_url += f"?{query_string}"
                    return request.redirect(old_url, code=302)
                
                # Category not found
                raise werkzeug.exceptions.NotFound()
            
            # Instead of redirecting, call the original controller directly
            # This keeps the clean URL in the browser while serving the content

            _logger.debug("Serving category content for clean URL /%s/%s (category ID: %s)",
                         parent_category, child_category, category.id)

            # Create WebsiteSale controller instance
            website_sale = WebsiteSale()

            # Call the original category controller with the category slug
            category_slug = request.env['ir.http']._slug(category)

            try:
                # Call the shop category method directly
                return website_sale.shop(category=category_slug, page=page, **kwargs)
            except Exception as e:
                _logger.error("Error calling original category controller: %s", str(e))
                # Fallback to redirect if direct call fails
                shop_url = f"/shop/category/{category_slug}"
                if page > 1:
                    shop_url += f"/page/{page}"
                if kwargs:
                    query_string = werkzeug.urls.url_encode(kwargs)
                    shop_url += f"?{query_string}"
                return request.redirect(shop_url, code=301)
            
        except werkzeug.exceptions.NotFound:
            raise
        except Exception as e:
            _logger.error("Error handling category URL /%s/%s: %s", 
                         parent_category, child_category, str(e))
            raise werkzeug.exceptions.NotFound()
    
    @http.route([
        '/<string:single_category>',
    ], type='http', auth="public", website=True, sitemap=True)
    def single_category_clean_url(self, single_category, page=1, **kwargs):
        """Handle single-level category URLs like /sofas
        
        Args:
            single_category (str): Category slug
            page (int): Page number for pagination
            **kwargs: Additional query parameters
            
        Returns:
            werkzeug.Response: Redirect to existing shop controller
        """
        try:
            # Skip if this looks like a product URL or other known pattern
            if self._should_skip_single_category(single_category):
                raise werkzeug.exceptions.NotFound()

            # Ensure we have a valid database connection
            if not request.env or not hasattr(request.env, 'cr') or not request.env.cr:
                raise werkzeug.exceptions.NotFound()

            # Find single-level category
            Category = request.env['product.public.category']
            name_pattern = single_category.replace('-', ' ')

            categories = Category.search([
                ('parent_id', '=', False),
                ('name', 'ilike', name_pattern)
            ])

            category = None
            for cat in categories:
                if request.env['ir.http']._slugify_clean(cat.name) == single_category:
                    category = cat
                    break

            if not category:
                # Check URL mapping
                clean_url = f"/{single_category}"
                mapping = request.env['url.mapping'].find_by_new_url(clean_url)
                if mapping and mapping.record_exists:
                    old_url = mapping.old_url
                    if page > 1:
                        old_url += f"/page/{page}"
                    if kwargs:
                        query_string = werkzeug.urls.url_encode(kwargs)
                        old_url += f"?{query_string}"
                    return request.redirect(old_url, code=302)
                
                raise werkzeug.exceptions.NotFound()
            
            # Instead of redirecting, call the original controller directly
            _logger.debug("Serving single category content for clean URL /%s (category ID: %s)",
                         single_category, category.id)

            # Create WebsiteSale controller instance
            website_sale = WebsiteSale()

            # Call the original category controller
            category_slug = request.env['ir.http']._slug(category)

            try:
                return website_sale.shop(category=category_slug, page=page, **kwargs)
            except Exception as e:
                _logger.error("Error calling original single category controller: %s", str(e))
                # Fallback to redirect
                shop_url = f"/shop/category/{category_slug}"
                if page > 1:
                    shop_url += f"/page/{page}"
                if kwargs:
                    query_string = werkzeug.urls.url_encode(kwargs)
                    shop_url += f"?{query_string}"
                return request.redirect(shop_url, code=301)
            
        except werkzeug.exceptions.NotFound:
            raise
        except Exception as e:
            _logger.error("Error handling single category URL /%s: %s", 
                         single_category, str(e))
            raise werkzeug.exceptions.NotFound()
    
    def _should_skip_single_category(self, url_part):
        """Check if URL part should be skipped for category routing
        
        Args:
            url_part (str): URL part to check
            
        Returns:
            bool: True if should skip, False otherwise
        """
        # Skip known patterns that are not categories
        skip_patterns = [
            'shop', 'blog', 'contactus', 'our-story', 'showroom',
            'warranty-claim-form', 'terms', 'privacy', 'web',
            'admin', 'api', 'static', 'assets'
        ]
        
        return url_part.lower() in skip_patterns
