# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import werkzeug
import logging

_logger = logging.getLogger(__name__)


class ProductRouting(http.Controller):
    
    @http.route([
        '/<string:product_name>',
    ], type='http', auth="public", website=True, sitemap=True)
    def product_clean_url(self, product_name, **kwargs):
        """Handle clean product URLs like /loft-2-seater
        
        This controller intercepts clean product URLs and redirects to the 
        existing product controller, preserving all functionality while showing
        clean URLs in the browser.
        
        Args:
            product_name (str): Product slug
            **kwargs: Additional query parameters including attribute_values
            
        Returns:
            werkzeug.Response: Redirect to existing product controller
        """
        try:
            # Skip if this looks like a category or other known pattern
            if self._should_skip_product(product_name):
                raise werkzeug.exceptions.NotFound()
            
            # Find product by clean name
            product = request.env['ir.http']._find_product_by_clean_name(product_name)
            
            if not product:
                # Check URL mapping for redirects
                clean_url = f"/{product_name}"
                mapping = request.env['url.mapping'].find_by_new_url(clean_url)
                if mapping and mapping.record_exists:
                    # Redirect to old URL if mapping exists
                    old_url = mapping.old_url
                    if kwargs:
                        query_string = werkzeug.urls.url_encode(kwargs)
                        old_url += f"?{query_string}"
                    return request.redirect(old_url, code=302)
                
                # Product not found
                raise werkzeug.exceptions.NotFound()
            
            # Handle attribute values in URL fragment
            fragment = ''
            if 'attribute_values' in kwargs:
                # Convert attribute names back to IDs for existing controller
                attribute_values = kwargs.pop('attribute_values')
                clean_attributes = self._convert_attribute_names_to_ids(
                    product, attribute_values
                )
                if clean_attributes:
                    fragment = f"#attribute_values={clean_attributes}"
                elif attribute_values:
                    # Keep original if conversion fails
                    fragment = f"#attribute_values={attribute_values}"
            
            # Build redirect URL to existing product controller
            product_url = f"/shop/{request.env['ir.http']._slug(product)}"
            
            # Preserve other query parameters
            if kwargs:
                query_string = werkzeug.urls.url_encode(kwargs)
                product_url += f"?{query_string}"
            
            product_url += fragment
            
            _logger.debug("Redirecting product URL /%s to %s", 
                         product_name, product_url)
            
            # 301 redirect to preserve SEO
            return request.redirect(product_url, code=301)
            
        except werkzeug.exceptions.NotFound:
            raise
        except Exception as e:
            _logger.error("Error handling product URL /%s: %s", 
                         product_name, str(e))
            raise werkzeug.exceptions.NotFound()
    
    def _should_skip_product(self, url_part):
        """Check if URL part should be skipped for product routing
        
        Args:
            url_part (str): URL part to check
            
        Returns:
            bool: True if should skip, False otherwise
        """
        # Skip known patterns that are not products
        skip_patterns = [
            'shop', 'blog', 'contactus', 'our-story', 'showroom',
            'warranty-claim-form', 'terms', 'privacy', 'web',
            'admin', 'api', 'static', 'assets', 'helpdesk'
        ]
        
        # Also skip if it looks like a category (contains common category words)
        category_indicators = [
            'sofas', 'chairs', 'tables', 'beds', 'storage',
            'lighting', 'rugs', 'accessories', 'outdoor'
        ]
        
        url_lower = url_part.lower()
        
        if url_lower in skip_patterns:
            return True
            
        # Skip if it's likely a category
        for indicator in category_indicators:
            if indicator in url_lower:
                return True
        
        return False
    
    def _convert_attribute_names_to_ids(self, product, attribute_names):
        """Convert clean attribute names back to IDs
        
        Args:
            product: Product record
            attribute_names (str): Comma-separated clean attribute names
            
        Returns:
            str: Comma-separated attribute value IDs
        """
        try:
            if not attribute_names:
                return ''
            
            clean_names = attribute_names.split(',')
            value_ids = []
            
            # Get product's attribute values
            attribute_values = product.attribute_line_ids.mapped('value_ids')
            
            for clean_name in clean_names:
                # Find matching attribute value
                for value in attribute_values:
                    if request.env['ir.http']._slugify_clean(value.name) == clean_name.strip():
                        value_ids.append(str(value.id))
                        break
            
            return ','.join(value_ids) if value_ids else ''
            
        except Exception as e:
            _logger.warning("Error converting attribute names %s to IDs: %s", 
                           attribute_names, str(e))
            return ''
    
    def _convert_attribute_ids_to_names(self, product, attribute_ids):
        """Convert attribute value IDs to clean names
        
        Args:
            product: Product record
            attribute_ids (str): Comma-separated attribute value IDs
            
        Returns:
            str: Comma-separated clean attribute names
        """
        try:
            if not attribute_ids:
                return ''
            
            try:
                value_ids = [int(x) for x in attribute_ids.split(',')]
            except ValueError:
                return attribute_ids
            
            values = request.env['product.attribute.value'].browse(value_ids)
            
            clean_names = []
            for value in values:
                if value.exists():
                    clean_name = request.env['ir.http']._slugify_clean(value.name)
                    clean_names.append(clean_name)
            
            return ','.join(clean_names) if clean_names else ''
            
        except Exception as e:
            _logger.warning("Error converting attribute IDs %s to names: %s", 
                           attribute_ids, str(e))
            return attribute_ids
