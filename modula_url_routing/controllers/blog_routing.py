# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import werkzeug
import logging

_logger = logging.getLogger(__name__)


class BlogRouting(http.Controller):
    
    @http.route([
        '/blog/<string:blog_name>',
        '/blog/<string:blog_name>/page/<int:page>',
        '/blog/<string:blog_name>/<string:post_name>',
    ], type='http', auth="public", website=True, sitemap=True)
    def blog_clean_url(self, blog_name, post_name=None, page=1, **kwargs):
        """Handle clean blog URLs like /blog/modula-living-journal
        
        This controller intercepts clean blog URLs and redirects to the 
        existing blog controller, preserving all functionality while showing
        clean URLs in the browser.
        
        Args:
            blog_name (str): Blog slug
            post_name (str, optional): Post slug
            page (int): Page number for pagination
            **kwargs: Additional query parameters
            
        Returns:
            werkzeug.Response: Redirect to existing blog controller
        """
        try:
            # Find blog by clean name
            blog = request.env['ir.http']._find_blog_by_clean_name(blog_name)
            if not blog:
                # Check URL mapping for blog
                clean_url = f"/blog/{blog_name}"
                mapping = request.env['url.mapping'].find_by_new_url(clean_url)
                if mapping and mapping.record_exists:
                    old_url = mapping.old_url
                    if post_name:
                        # This is a post URL, need to handle differently
                        return self._handle_post_url_from_mapping(
                            mapping, post_name, **kwargs
                        )
                    elif page > 1:
                        old_url += f"/page/{page}"
                    if kwargs:
                        query_string = werkzeug.urls.url_encode(kwargs)
                        old_url += f"?{query_string}"
                    return request.redirect(old_url, code=302)
                
                raise werkzeug.exceptions.NotFound()
            
            if post_name:
                # Handle blog post URL
                return self._handle_blog_post_url(blog, post_name, **kwargs)
            else:
                # Handle blog listing URL
                return self._handle_blog_listing_url(blog, page, **kwargs)
                
        except werkzeug.exceptions.NotFound:
            raise
        except Exception as e:
            _logger.error("Error handling blog URL /blog/%s%s: %s", 
                         blog_name, f"/{post_name}" if post_name else "", str(e))
            raise werkzeug.exceptions.NotFound()
    
    def _handle_blog_post_url(self, blog, post_name, **kwargs):
        """Handle blog post URL
        
        Args:
            blog: Blog record
            post_name (str): Post slug
            **kwargs: Additional query parameters
            
        Returns:
            werkzeug.Response: Redirect to existing blog post controller
        """
        try:
            # Find post by clean name
            post = request.env['ir.http']._find_post_by_clean_name(blog, post_name)
            if not post:
                # Check URL mapping for post
                clean_url = f"/blog/{request.env['ir.http']._slugify_clean(blog.name)}/{post_name}"
                mapping = request.env['url.mapping'].find_by_new_url(clean_url)
                if mapping and mapping.record_exists:
                    old_url = mapping.old_url
                    if kwargs:
                        query_string = werkzeug.urls.url_encode(kwargs)
                        old_url += f"?{query_string}"
                    return request.redirect(old_url, code=302)
                
                raise werkzeug.exceptions.NotFound()
            
            # Build redirect URL to existing blog post controller
            post_url = f"/blog/{request.env['ir.http']._slug(blog)}/{request.env['ir.http']._slug(post)}"
            
            if kwargs:
                query_string = werkzeug.urls.url_encode(kwargs)
                post_url += f"?{query_string}"
            
            _logger.debug("Redirecting blog post URL to %s", post_url)
            
            return request.redirect(post_url, code=301)
            
        except Exception as e:
            _logger.error("Error handling blog post URL: %s", str(e))
            raise werkzeug.exceptions.NotFound()
    
    def _handle_blog_listing_url(self, blog, page, **kwargs):
        """Handle blog listing URL
        
        Args:
            blog: Blog record
            page (int): Page number
            **kwargs: Additional query parameters
            
        Returns:
            werkzeug.Response: Redirect to existing blog controller
        """
        try:
            # Build redirect URL to existing blog controller
            blog_url = f"/blog/{request.env['ir.http']._slug(blog)}"
            if page > 1:
                blog_url += f"/page/{page}"
            
            if kwargs:
                query_string = werkzeug.urls.url_encode(kwargs)
                blog_url += f"?{query_string}"
            
            _logger.debug("Redirecting blog listing URL to %s", blog_url)
            
            return request.redirect(blog_url, code=301)
            
        except Exception as e:
            _logger.error("Error handling blog listing URL: %s", str(e))
            raise werkzeug.exceptions.NotFound()
    
    def _handle_post_url_from_mapping(self, blog_mapping, post_name, **kwargs):
        """Handle post URL when blog was found via mapping
        
        Args:
            blog_mapping: URL mapping record for blog
            post_name (str): Post slug
            **kwargs: Additional query parameters
            
        Returns:
            werkzeug.Response: Redirect to existing blog post controller
        """
        try:
            # Get the blog from mapping
            if blog_mapping.model_name == 'blog.blog':
                blog = request.env['blog.blog'].browse(blog_mapping.record_id)
                if blog.exists():
                    return self._handle_blog_post_url(blog, post_name, **kwargs)
            
            # Fallback: try to construct URL from mapping
            base_old_url = blog_mapping.old_url
            post_url = f"{base_old_url}/{post_name}"
            
            if kwargs:
                query_string = werkzeug.urls.url_encode(kwargs)
                post_url += f"?{query_string}"
            
            return request.redirect(post_url, code=302)
            
        except Exception as e:
            _logger.error("Error handling post URL from mapping: %s", str(e))
            raise werkzeug.exceptions.NotFound()
