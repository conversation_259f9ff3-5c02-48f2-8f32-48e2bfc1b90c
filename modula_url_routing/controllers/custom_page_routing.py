# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import werkzeug
import logging

_logger = logging.getLogger(__name__)


class CustomPageRouting(http.Controller):
    
    # Custom page mappings - clean URL to target URL
    CUSTOM_PAGES = {
        'warranty-claim-form': '/helpdesk/warranty-claim-2',
        'contact-us': '/contactus',
        'our-story': '/our-story',
        'showroom': '/showroom',
        'terms-and-conditions': '/terms',
        'privacy-policy': '/privacy',
        'care-and-maintenance': '/care-and-maintenance',
        'shipping-and-delivery': '/shipping-and-delivery',
        'returns-and-refunds': '/returns-and-refunds',
    }
    
    @http.route([
        '/warranty-claim-form',
        '/contact-us', 
        '/terms-and-conditions',
        '/privacy-policy',
        '/care-and-maintenance',
        '/shipping-and-delivery',
        '/returns-and-refunds',
    ], type='http', auth="public", website=True, sitemap=True)
    def custom_page_routing(self, **kwargs):
        """Handle custom page clean URLs
        
        This controller handles specific custom page URLs that need clean,
        SEO-friendly URLs different from their internal Odoo URLs.
        
        Args:
            **kwargs: Query parameters
            
        Returns:
            werkzeug.Response: Redirect to existing page controller
        """
        try:
            # Get current path
            current_path = request.httprequest.path.strip('/')
            
            # Find target URL
            target_url = self.CUSTOM_PAGES.get(current_path)
            if not target_url:
                # Check URL mapping as fallback
                clean_url = f"/{current_path}"
                mapping = request.env['url.mapping'].find_by_new_url(clean_url)
                if mapping and mapping.record_exists:
                    target_url = mapping.old_url
                else:
                    raise werkzeug.exceptions.NotFound()
            
            # Preserve query parameters
            if kwargs:
                query_string = werkzeug.urls.url_encode(kwargs)
                target_url += f"?{query_string}"
            
            _logger.debug("Redirecting custom page URL /%s to %s", 
                         current_path, target_url)
            
            # 301 redirect to preserve SEO
            return request.redirect(target_url, code=301)
            
        except werkzeug.exceptions.NotFound:
            raise
        except Exception as e:
            _logger.error("Error handling custom page URL: %s", str(e))
            raise werkzeug.exceptions.NotFound()
    
    @http.route([
        '/<string:custom_page>',
    ], type='http', auth="public", website=True, sitemap=False)
    def generic_custom_page_routing(self, custom_page, **kwargs):
        """Handle generic custom page routing as fallback
        
        This is a lower-priority route that handles any remaining
        single-level URLs that might be custom pages.
        
        Args:
            custom_page (str): Custom page slug
            **kwargs: Query parameters
            
        Returns:
            werkzeug.Response: Redirect to existing page controller
        """
        try:
            # Skip if this is handled by other controllers
            if self._should_skip_generic_routing(custom_page):
                raise werkzeug.exceptions.NotFound()
            
            # Check if it's in our custom pages mapping
            target_url = self.CUSTOM_PAGES.get(custom_page)
            if target_url:
                if kwargs:
                    query_string = werkzeug.urls.url_encode(kwargs)
                    target_url += f"?{query_string}"
                
                return request.redirect(target_url, code=301)
            
            # Check URL mapping
            clean_url = f"/{custom_page}"
            mapping = request.env['url.mapping'].find_by_new_url(clean_url)
            if mapping and mapping.record_exists:
                target_url = mapping.old_url
                if kwargs:
                    query_string = werkzeug.urls.url_encode(kwargs)
                    target_url += f"?{query_string}"
                
                return request.redirect(target_url, code=302)
            
            # Not found
            raise werkzeug.exceptions.NotFound()
            
        except werkzeug.exceptions.NotFound:
            raise
        except Exception as e:
            _logger.error("Error handling generic custom page URL /%s: %s", 
                         custom_page, str(e))
            raise werkzeug.exceptions.NotFound()
    
    def _should_skip_generic_routing(self, url_part):
        """Check if URL part should be skipped for generic custom page routing
        
        Args:
            url_part (str): URL part to check
            
        Returns:
            bool: True if should skip, False otherwise
        """
        # Skip patterns handled by other controllers or systems
        skip_patterns = [
            'shop', 'blog', 'web', 'admin', 'api', 'static', 'assets',
            'helpdesk', 'portal', 'payment', 'auth_signup', 'website',
            'mail', 'calendar', 'survey', 'event', 'forum', 'slides',
            'elearning', 'hr', 'project', 'timesheet', 'crm', 'sale',
            'purchase', 'stock', 'account', 'mrp', 'maintenance'
        ]
        
        # Skip if it looks like a product or category
        # (these should be handled by product/category controllers)
        product_category_indicators = [
            'sofa', 'chair', 'table', 'bed', 'storage', 'lighting',
            'rug', 'accessory', 'outdoor', 'dining', 'living', 'bedroom'
        ]
        
        url_lower = url_part.lower()
        
        # Skip known system patterns
        if url_lower in skip_patterns:
            return True
        
        # Skip if it contains product/category indicators
        for indicator in product_category_indicators:
            if indicator in url_lower:
                return True
        
        # Skip if it looks like a file extension
        if '.' in url_part:
            return True
        
        # Skip very short URLs (likely system URLs)
        if len(url_part) <= 2:
            return True
        
        return False
    
    def get_custom_page_mappings(self):
        """Get all custom page mappings for sitemap generation

        Returns:
            dict: Custom page mappings
        """
        return self.CUSTOM_PAGES.copy()
