<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- URL Mapping List View -->
    <record id="view_url_mapping_list" model="ir.ui.view">
        <field name="name">url.mapping.list</field>
        <field name="model">url.mapping</field>
        <field name="arch" type="xml">
            <list string="URL Mappings" decoration-muted="not active">
                <field name="new_url" string="Clean URL"/>
                <field name="old_url" string="Original URL"/>
                <field name="model_name" string="Model"/>
                <field name="record_name" string="Record"/>
                <field name="redirect_type" string="Redirect"/>
                <field name="active" string="Active"/>
                <field name="record_exists" string="Valid" widget="boolean"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- URL Mapping Form View -->
    <record id="view_url_mapping_form" model="ir.ui.view">
        <field name="name">url.mapping.form</field>
        <field name="model">url.mapping</field>
        <field name="arch" type="xml">
            <form string="URL Mapping">
                <header>
                    <!-- Test button removed - no wizard model -->
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="new_url" placeholder="e.g., /product-name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="url_info">
                            <field name="old_url" placeholder="e.g., /shop/product-name-123"/>
                            <field name="redirect_type"/>
                        </group>
                        <group name="record_info">
                            <field name="model_name"/>
                            <field name="record_id"/>
                            <field name="record_name" readonly="1"/>
                            <field name="record_exists" readonly="1" widget="boolean"/>
                        </group>
                    </group>
                    
                    <group name="company_info" groups="base.group_multi_company">
                        <field name="company_id"/>
                    </group>
                    
                    <div class="alert alert-warning" role="alert" invisible="record_exists == True">
                        <strong>Warning:</strong> The referenced record no longer exists. This mapping may not work correctly.
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- URL Mapping Search View -->
    <record id="view_url_mapping_search" model="ir.ui.view">
        <field name="name">url.mapping.search</field>
        <field name="model">url.mapping</field>
        <field name="arch" type="xml">
            <search string="URL Mappings">
                <field name="new_url" string="Clean URL"/>
                <field name="old_url" string="Original URL"/>
                <field name="model_name" string="Model"/>
                <field name="record_name" string="Record"/>
                
                <filter name="active" string="Active" domain="[('active', '=', True)]"/>
                <filter name="inactive" string="Inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter name="valid_records" string="Valid Records" domain="[('record_exists', '=', True)]"/>
                <filter name="invalid_records" string="Invalid Records" domain="[('record_exists', '=', False)]"/>
                <separator/>
                <filter name="products" string="Products" domain="[('model_name', '=', 'product.template')]"/>
                <filter name="categories" string="Categories" domain="[('model_name', '=', 'product.public.category')]"/>
                <filter name="blogs" string="Blogs" domain="[('model_name', '=', 'blog.blog')]"/>
                <filter name="blog_posts" string="Blog Posts" domain="[('model_name', '=', 'blog.post')]"/>
                <filter name="custom_pages" string="Custom Pages" domain="[('model_name', '=', 'website.page')]"/>
                
                <group expand="0" string="Group By">
                    <filter name="group_model" string="Model" context="{'group_by': 'model_name'}"/>
                    <filter name="group_redirect_type" string="Redirect Type" context="{'group_by': 'redirect_type'}"/>
                    <filter name="group_active" string="Status" context="{'group_by': 'active'}"/>
                    <filter name="group_company" string="Company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <!-- URL Mapping Action -->
    <record id="action_url_mapping" model="ir.actions.act_window">
        <field name="name">URL Mappings</field>
        <field name="res_model">url.mapping</field>
        <field name="path">url-mappings</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first URL mapping!
            </p>
            <p>
                URL mappings allow you to redirect old URLs to new clean URLs.
                This helps maintain SEO rankings when changing URL structures.
            </p>
        </field>
    </record>

    <!-- Test URL Mapping Action - Removed (model doesn't exist) -->

    <!-- Menu Items -->
    <menuitem id="menu_url_routing_root" 
              name="URL Routing" 
              parent="website.menu_website_configuration" 
              sequence="50"
              groups="website.group_website_designer"/>
              
    <menuitem id="menu_url_mappings" 
              name="URL Mappings" 
              parent="menu_url_routing_root" 
              action="action_url_mapping" 
              sequence="10"/>

    <!-- URL Mapping Dashboard -->
    <!-- <record id="view_url_mapping_dashboard" model="ir.ui.view">
        <field name="name">url.mapping.dashboard</field>
        <field name="model">url.mapping</field>
        <field name="arch" type="xml">
            <dashboard>
                <view type="graph" ref="view_url_mapping_graph"/>
                <group string="URL Mapping Statistics">
                    <aggregate name="total_mappings" string="Total Mappings" field="id" group_operator="count"/>
                    <aggregate name="active_mappings" string="Active Mappings" field="id" group_operator="count" domain="[('active', '=', True)]"/>
                    <aggregate name="product_mappings" string="Product Mappings" field="id" group_operator="count" domain="[('model_name', '=', 'product.template')]"/>
                    <aggregate name="category_mappings" string="Category Mappings" field="id" group_operator="count" domain="[('model_name', '=', 'product.public.category')]"/>
                </group>
            </dashboard>
        </field>
    </record> -->

    <!-- URL Mapping Graph View -->
    <record id="view_url_mapping_graph" model="ir.ui.view">
        <field name="name">url.mapping.graph</field>
        <field name="model">url.mapping</field>
        <field name="arch" type="xml">
            <graph string="URL Mappings by Model" type="pie">
                <field name="model_name"/>
            </graph>
        </field>
    </record>

    <!-- URL Mapping Pivot View -->
    <record id="view_url_mapping_pivot" model="ir.ui.view">
        <field name="name">url.mapping.pivot</field>
        <field name="model">url.mapping</field>
        <field name="arch" type="xml">
            <pivot string="URL Mapping Analysis">
                <field name="model_name" type="row"/>
                <field name="active" type="col"/>
                <field name="id" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Add pivot and graph to action -->
    <record id="action_url_mapping_analysis" model="ir.actions.act_window">
        <field name="name">URL Mapping Analysis</field>
        <field name="res_model">url.mapping</field>
        <field name="path">url-mapping-analysis</field>
        <field name="view_mode">graph,pivot</field>
        <field name="context">{}</field>
    </record>

    <menuitem id="menu_url_mapping_analysis" 
              name="URL Analysis" 
              parent="menu_url_routing_root" 
              action="action_url_mapping_analysis" 
              sequence="20"
              groups="base.group_system"/>

</odoo>
