# -*- coding: utf-8 -*-

from . import controllers
from . import models

def post_init_hook(env):
    """Create initial URL mappings after module installation"""
    try:
        from .data.create_url_mappings import create_initial_url_mappings
        create_initial_url_mappings(env)
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.warning("Could not create initial URL mappings: %s", str(e))

def uninstall_hook(env):
    """Clean up URL mappings when module is uninstalled"""
    try:
        # Remove all URL mappings
        env['url.mapping'].search([]).unlink()
        
        # Restore original URLs by recomputing them
        products = env['product.template'].search([])
        products._compute_website_url()
        
        categories = env['product.public.category'].search([])
        categories._compute_website_url()
        
        blogs = env['blog.blog'].search([])
        blogs._compute_website_url()
        
        posts = env['blog.post'].search([])
        posts._compute_website_url()
        
    except Exception as e:
        import logging
        _logger = logging.getLogger(__name__)
        _logger.warning("Could not clean up URL mappings: %s", str(e))
