# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.tools import ormcache
import logging

_logger = logging.getLogger(__name__)


class UrlMapping(models.Model):
    _name = 'url.mapping'
    _description = 'URL Mapping for Clean URLs'
    _rec_name = 'new_url'
    _order = 'create_date desc'
    
    old_url = fields.Char('Old URL', required=True, index=True, 
                         help="Original Odoo URL with ID (e.g., /shop/product-name-123)")
    new_url = fields.Char('New URL', required=True, index=True,
                         help="Clean URL without ID (e.g., /product-name)")
    model_name = fields.Char('Model Name', required=True, index=True,
                           help="Odoo model name (e.g., product.template)")
    record_id = fields.Integer('Record ID', required=True, index=True,
                             help="ID of the record this mapping refers to")
    redirect_type = fields.Selection([
        ('301', '301 Permanent'),
        ('302', '302 Temporary')
    ], default='301', required=True, string='Redirect Type',
       help="Type of HTTP redirect to use")
    active = fields.Boolean('Active', default=True, index=True,
                          help="Whether this mapping is active")
    company_id = fields.Many2one('res.company', string='Company', 
                                default=lambda self: self.env.company,
                                help="Company this mapping belongs to")
    
    # Computed fields for better UX
    record_name = fields.Char('Record Name', compute='_compute_record_info', store=True)
    record_exists = fields.Boolean('Record Exists', compute='_compute_record_info', store=True)
    
    @api.depends('model_name', 'record_id')
    def _compute_record_info(self):
        """Compute record information for display"""
        for mapping in self:
            try:
                if mapping.model_name and mapping.record_id:
                    record = self.env[mapping.model_name].browse(mapping.record_id)
                    if record.exists():
                        mapping.record_name = record.display_name
                        mapping.record_exists = True
                    else:
                        mapping.record_name = f"Deleted {mapping.model_name} (ID: {mapping.record_id})"
                        mapping.record_exists = False
                else:
                    mapping.record_name = "Invalid mapping"
                    mapping.record_exists = False
            except Exception as e:
                mapping.record_name = f"Error: {str(e)}"
                mapping.record_exists = False
    
    @api.constrains('new_url')
    def _check_unique_new_url(self):
        """Ensure new URLs are unique"""
        for record in self:
            existing = self.search([
                ('new_url', '=', record.new_url),
                ('id', '!=', record.id),
                ('active', '=', True)
            ])
            if existing:
                raise ValidationError(_('New URL must be unique: %s') % record.new_url)
    
    @api.constrains('old_url', 'new_url')
    def _check_url_format(self):
        """Validate URL format"""
        for record in self:
            if not record.old_url.startswith('/'):
                raise ValidationError(_('Old URL must start with /'))
            if not record.new_url.startswith('/'):
                raise ValidationError(_('New URL must start with /'))
    
    @ormcache('old_url')
    def _get_mapping_by_old_url(self, old_url):
        """Cached lookup for URL mappings by old URL
        
        Args:
            old_url (str): Old URL to find mapping for
            
        Returns:
            recordset: URL mapping record or empty recordset
        """
        return self.search([('old_url', '=', old_url), ('active', '=', True)], limit=1)
    
    @ormcache('new_url')
    def _get_mapping_by_new_url(self, new_url):
        """Cached lookup for URL mappings by new URL
        
        Args:
            new_url (str): New URL to find mapping for
            
        Returns:
            recordset: URL mapping record or empty recordset
        """
        return self.search([('new_url', '=', new_url), ('active', '=', True)], limit=1)
    
    def find_by_old_url(self, old_url):
        """Find mapping by old URL
        
        Args:
            old_url (str): Old URL to search for
            
        Returns:
            recordset: Found mapping or empty recordset
        """
        try:
            return self._get_mapping_by_old_url(old_url)
        except Exception as e:
            _logger.warning("Error finding mapping by old URL %s: %s", old_url, str(e))
            return self.env['url.mapping']
    
    def find_by_new_url(self, new_url):
        """Find mapping by new URL
        
        Args:
            new_url (str): New URL to search for
            
        Returns:
            recordset: Found mapping or empty recordset
        """
        try:
            return self._get_mapping_by_new_url(new_url)
        except Exception as e:
            _logger.warning("Error finding mapping by new URL %s: %s", new_url, str(e))
            return self.env['url.mapping']
    
    def clear_cache(self):
        """Clear URL mapping cache"""
        try:
            # Clear cache for specific ormcache methods
            self._get_mapping_by_old_url.clear_cache(self)
            self._get_mapping_by_new_url.clear_cache(self)
        except Exception as e:
            _logger.warning("Could not clear URL mapping cache: %s", str(e))
    
    @api.model_create_multi
    def create(self, vals_list):
        """Clear cache when creating new mappings"""
        mappings = super().create(vals_list)
        self.clear_cache()
        return mappings
    
    def write(self, vals):
        """Clear cache when updating mappings"""
        result = super().write(vals)
        if 'old_url' in vals or 'new_url' in vals or 'active' in vals:
            self.clear_cache()
        return result
    
    def unlink(self):
        """Clear cache when deleting mappings"""
        self.clear_cache()
        return super().unlink()
    
    @api.model
    def create_mapping(self, old_url, new_url, model_name, record_id, redirect_type='301'):
        """Helper method to create URL mapping
        
        Args:
            old_url (str): Original URL
            new_url (str): Clean URL
            model_name (str): Model name
            record_id (int): Record ID
            redirect_type (str): Redirect type (301 or 302)
            
        Returns:
            recordset: Created mapping
        """
        try:
            # Check if mapping already exists
            existing = self.search([
                ('old_url', '=', old_url),
                ('model_name', '=', model_name),
                ('record_id', '=', record_id)
            ], limit=1)
            
            if existing:
                # Update existing mapping
                existing.write({
                    'new_url': new_url,
                    'redirect_type': redirect_type,
                    'active': True
                })
                return existing
            else:
                # Create new mapping
                return self.create({
                    'old_url': old_url,
                    'new_url': new_url,
                    'model_name': model_name,
                    'record_id': record_id,
                    'redirect_type': redirect_type,
                })
        except Exception as e:
            _logger.error("Error creating URL mapping %s -> %s: %s", old_url, new_url, str(e))
            return self.env['url.mapping']
    
    @api.model
    def cleanup_invalid_mappings(self):
        """Clean up mappings for deleted records"""
        invalid_mappings = []
        
        for mapping in self.search([('active', '=', True)]):
            try:
                record = self.env[mapping.model_name].browse(mapping.record_id)
                if not record.exists():
                    invalid_mappings.append(mapping.id)
            except Exception:
                invalid_mappings.append(mapping.id)
        
        if invalid_mappings:
            self.browse(invalid_mappings).write({'active': False})
            _logger.info("Deactivated %d invalid URL mappings", len(invalid_mappings))
        
        return len(invalid_mappings)

    @api.model
    def test_url_mapping(self, test_url):
        """Test URL mapping functionality

        Args:
            test_url (str): URL to test

        Returns:
            dict: Test result
        """
        try:
            if not test_url or not test_url.startswith('/'):
                return {
                    'success': False,
                    'error': 'URL must start with /'
                }

            # Check if it's a clean URL that has a mapping
            mapping = self.find_by_new_url(test_url)
            if mapping:
                return {
                    'success': True,
                    'redirect_url': mapping.old_url,
                    'message': f'Clean URL redirects to {mapping.old_url}'
                }

            # Check if it's an old URL that has a mapping
            mapping = self.find_by_old_url(test_url)
            if mapping:
                return {
                    'success': True,
                    'redirect_url': mapping.new_url,
                    'message': f'Old URL redirects to {mapping.new_url}'
                }

            return {
                'success': False,
                'error': 'No URL mapping found for this URL'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    @api.model
    def generate_clean_url_suggestion(self, old_url):
        """Generate clean URL suggestion from old URL

        Args:
            old_url (str): Old URL to generate suggestion for

        Returns:
            dict: Generation result
        """
        try:
            if not old_url or not old_url.startswith('/'):
                return {
                    'success': False,
                    'error': 'URL must start with /'
                }

            # Extract meaningful parts from old URL
            if '/shop/' in old_url:
                # Product or category URL
                parts = old_url.replace('/shop/', '').split('/')
                if len(parts) >= 1:
                    # Remove ID suffix pattern
                    clean_part = parts[-1]
                    # Remove -ID pattern at the end
                    import re
                    clean_part = re.sub(r'-\d+$', '', clean_part)

                    if 'category' in old_url and len(parts) >= 2:
                        # Category URL - extract category name
                        category_part = parts[1]
                        category_part = re.sub(r'-\d+$', '', category_part)
                        # Try to split into parent/child
                        category_words = category_part.split('-')
                        if len(category_words) >= 2:
                            # Assume first half is parent, second half is child
                            mid = len(category_words) // 2
                            parent = '-'.join(category_words[:mid])
                            child = '-'.join(category_words[mid:])
                            clean_url = f"/{parent}/{child}"
                        else:
                            clean_url = f"/{category_part}"
                    else:
                        # Product URL
                        clean_url = f"/{clean_part}"

                    return {
                        'success': True,
                        'clean_url': clean_url
                    }

            elif '/blog/' in old_url:
                # Blog URL
                parts = old_url.replace('/blog/', '').split('/')
                clean_parts = []
                for part in parts:
                    # Remove ID suffix
                    import re
                    clean_part = re.sub(r'-\d+$', '', part)
                    clean_parts.append(clean_part)

                clean_url = '/blog/' + '/'.join(clean_parts)
                return {
                    'success': True,
                    'clean_url': clean_url
                }

            return {
                'success': False,
                'error': 'Cannot generate suggestion for this URL pattern'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
