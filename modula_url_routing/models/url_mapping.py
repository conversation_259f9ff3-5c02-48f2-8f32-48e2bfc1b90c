# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.tools import ormcache
import logging

_logger = logging.getLogger(__name__)


class UrlMapping(models.Model):
    _name = 'url.mapping'
    _description = 'URL Mapping for Clean URLs'
    _rec_name = 'new_url'
    _order = 'create_date desc'
    
    old_url = fields.Char('Old URL', required=True, index=True, 
                         help="Original Odoo URL with ID (e.g., /shop/product-name-123)")
    new_url = fields.Char('New URL', required=True, index=True,
                         help="Clean URL without ID (e.g., /product-name)")
    model_name = fields.Char('Model Name', required=True, index=True,
                           help="Odoo model name (e.g., product.template)")
    record_id = fields.Integer('Record ID', required=True, index=True,
                             help="ID of the record this mapping refers to")
    redirect_type = fields.Selection([
        ('301', '301 Permanent'),
        ('302', '302 Temporary')
    ], default='301', required=True, string='Redirect Type',
       help="Type of HTTP redirect to use")
    active = fields.Boolean('Active', default=True, index=True,
                          help="Whether this mapping is active")
    company_id = fields.Many2one('res.company', string='Company', 
                                default=lambda self: self.env.company,
                                help="Company this mapping belongs to")
    
    # Computed fields for better UX
    record_name = fields.Char('Record Name', compute='_compute_record_info', store=False)
    record_exists = fields.Boolean('Record Exists', compute='_compute_record_info', store=False)
    
    @api.depends('model_name', 'record_id')
    def _compute_record_info(self):
        """Compute record information for display"""
        for mapping in self:
            try:
                if mapping.model_name and mapping.record_id:
                    record = self.env[mapping.model_name].browse(mapping.record_id)
                    if record.exists():
                        mapping.record_name = record.display_name
                        mapping.record_exists = True
                    else:
                        mapping.record_name = f"Deleted {mapping.model_name} (ID: {mapping.record_id})"
                        mapping.record_exists = False
                else:
                    mapping.record_name = "Invalid mapping"
                    mapping.record_exists = False
            except Exception as e:
                mapping.record_name = f"Error: {str(e)}"
                mapping.record_exists = False
    
    @api.constrains('new_url')
    def _check_unique_new_url(self):
        """Ensure new URLs are unique"""
        for record in self:
            existing = self.search([
                ('new_url', '=', record.new_url),
                ('id', '!=', record.id),
                ('active', '=', True)
            ])
            if existing:
                raise ValidationError(_('New URL must be unique: %s') % record.new_url)
    
    @api.constrains('old_url', 'new_url')
    def _check_url_format(self):
        """Validate URL format"""
        for record in self:
            if not record.old_url.startswith('/'):
                raise ValidationError(_('Old URL must start with /'))
            if not record.new_url.startswith('/'):
                raise ValidationError(_('New URL must start with /'))
    
    @ormcache('old_url')
    def _get_mapping_by_old_url(self, old_url):
        """Cached lookup for URL mappings by old URL
        
        Args:
            old_url (str): Old URL to find mapping for
            
        Returns:
            recordset: URL mapping record or empty recordset
        """
        return self.search([('old_url', '=', old_url), ('active', '=', True)], limit=1)
    
    @ormcache('new_url')
    def _get_mapping_by_new_url(self, new_url):
        """Cached lookup for URL mappings by new URL
        
        Args:
            new_url (str): New URL to find mapping for
            
        Returns:
            recordset: URL mapping record or empty recordset
        """
        return self.search([('new_url', '=', new_url), ('active', '=', True)], limit=1)
    
    def find_by_old_url(self, old_url):
        """Find mapping by old URL
        
        Args:
            old_url (str): Old URL to search for
            
        Returns:
            recordset: Found mapping or empty recordset
        """
        try:
            return self._get_mapping_by_old_url(old_url)
        except Exception as e:
            _logger.warning("Error finding mapping by old URL %s: %s", old_url, str(e))
            return self.env['url.mapping']
    
    def find_by_new_url(self, new_url):
        """Find mapping by new URL
        
        Args:
            new_url (str): New URL to search for
            
        Returns:
            recordset: Found mapping or empty recordset
        """
        try:
            return self._get_mapping_by_new_url(new_url)
        except Exception as e:
            _logger.warning("Error finding mapping by new URL %s: %s", new_url, str(e))
            return self.env['url.mapping']
    
    def clear_cache(self):
        """Clear URL mapping cache"""
        self._get_mapping_by_old_url.clear_cache(self)
        self._get_mapping_by_new_url.clear_cache(self)
    
    @api.model_create_multi
    def create(self, vals_list):
        """Clear cache when creating new mappings"""
        mappings = super().create(vals_list)
        self.clear_cache()
        return mappings
    
    def write(self, vals):
        """Clear cache when updating mappings"""
        result = super().write(vals)
        if 'old_url' in vals or 'new_url' in vals or 'active' in vals:
            self.clear_cache()
        return result
    
    def unlink(self):
        """Clear cache when deleting mappings"""
        self.clear_cache()
        return super().unlink()
    
    @api.model
    def create_mapping(self, old_url, new_url, model_name, record_id, redirect_type='301'):
        """Helper method to create URL mapping
        
        Args:
            old_url (str): Original URL
            new_url (str): Clean URL
            model_name (str): Model name
            record_id (int): Record ID
            redirect_type (str): Redirect type (301 or 302)
            
        Returns:
            recordset: Created mapping
        """
        try:
            # Check if mapping already exists
            existing = self.search([
                ('old_url', '=', old_url),
                ('model_name', '=', model_name),
                ('record_id', '=', record_id)
            ], limit=1)
            
            if existing:
                # Update existing mapping
                existing.write({
                    'new_url': new_url,
                    'redirect_type': redirect_type,
                    'active': True
                })
                return existing
            else:
                # Create new mapping
                return self.create({
                    'old_url': old_url,
                    'new_url': new_url,
                    'model_name': model_name,
                    'record_id': record_id,
                    'redirect_type': redirect_type,
                })
        except Exception as e:
            _logger.error("Error creating URL mapping %s -> %s: %s", old_url, new_url, str(e))
            return self.env['url.mapping']
    
    @api.model
    def cleanup_invalid_mappings(self):
        """Clean up mappings for deleted records"""
        invalid_mappings = []
        
        for mapping in self.search([('active', '=', True)]):
            try:
                record = self.env[mapping.model_name].browse(mapping.record_id)
                if not record.exists():
                    invalid_mappings.append(mapping.id)
            except Exception:
                invalid_mappings.append(mapping.id)
        
        if invalid_mappings:
            self.browse(invalid_mappings).write({'active': False})
            _logger.info("Deactivated %d invalid URL mappings", len(invalid_mappings))
        
        return len(invalid_mappings)
