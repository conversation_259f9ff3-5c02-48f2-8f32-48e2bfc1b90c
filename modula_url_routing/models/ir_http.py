# -*- coding: utf-8 -*-

from odoo import models, api
import re
import logging

_logger = logging.getLogger(__name__)


class IrHttp(models.AbstractModel):
    _inherit = 'ir.http'
    
    @classmethod
    def _slugify_clean(cls, name):
        """Generate clean slug without ID suffix
        
        Args:
            name (str): Name to slugify
            
        Returns:
            str: Clean slug without ID
        """
        if not name:
            return ''
        
        # Convert to lowercase and replace spaces/special chars with hyphens
        slug = re.sub(r'[^\w\s-]', '', name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')
    
    @classmethod
    def _generate_clean_url(cls, record, url_pattern=None):
        """Generate clean URL for any record
        
        Args:
            record: Odoo record
            url_pattern (str, optional): Custom URL pattern
            
        Returns:
            str: Clean URL
        """
        if not record or not record.id:
            return ''
        
        if url_pattern:
            return url_pattern.format(record=record)
        
        # Default pattern: use slugified name
        clean_name = cls._slugify_clean(record.display_name)
        return f"/{clean_name}"
    
    def _find_record_by_clean_url(self, model_name, clean_url):
        """Find record by clean URL pattern
        
        Args:
            model_name (str): Model name to search
            clean_url (str): Clean URL to match
            
        Returns:
            recordset: Found record or empty recordset
        """
        try:
            # Extract name from URL
            url_parts = clean_url.strip('/').split('/')
            if not url_parts:
                return self.env[model_name]
            
            # Search by name pattern
            Model = self.env[model_name]
            name_pattern = url_parts[-1].replace('-', ' ')
            
            # Try exact match first
            records = Model.search([('name', 'ilike', name_pattern)])
            if len(records) == 1:
                return records
            
            # Try fuzzy matching if multiple or no results
            for record in records:
                if self._slugify_clean(record.name) == url_parts[-1]:
                    return record
            
            return self.env[model_name]
            
        except Exception as e:
            _logger.warning("Error finding record by clean URL %s: %s", clean_url, str(e))
            return self.env[model_name]
    
    def _find_category_by_url_parts(self, parent_name, child_name):
        """Find category by parent and child URL parts
        
        Args:
            parent_name (str): Parent category URL part
            child_name (str): Child category URL part
            
        Returns:
            recordset: Found category or empty recordset
        """
        try:
            Category = self.env['product.public.category']
            
            # Convert URL parts back to names
            parent_pattern = parent_name.replace('-', ' ')
            child_pattern = child_name.replace('-', ' ')
            
            # Find parent category
            parent_categories = Category.search([
                ('parent_id', '=', False),
                ('name', 'ilike', parent_pattern)
            ])
            
            for parent in parent_categories:
                if self._slugify_clean(parent.name) == parent_name:
                    # Find child category
                    child_categories = Category.search([
                        ('parent_id', '=', parent.id),
                        ('name', 'ilike', child_pattern)
                    ])
                    
                    for child in child_categories:
                        if self._slugify_clean(child.name) == child_name:
                            return child
            
            return self.env['product.public.category']
            
        except Exception as e:
            _logger.warning("Error finding category by URL parts %s/%s: %s", 
                          parent_name, child_name, str(e))
            return self.env['product.public.category']
    
    def _find_product_by_clean_name(self, product_name):
        """Find product by clean name
        
        Args:
            product_name (str): Clean product name from URL
            
        Returns:
            recordset: Found product or empty recordset
        """
        try:
            Product = self.env['product.template']
            
            # Convert URL name back to search pattern
            name_pattern = product_name.replace('-', ' ')
            
            # Search for products
            products = Product.search([
                ('website_published', '=', True),
                ('name', 'ilike', name_pattern)
            ])
            
            # Find exact match by slug
            for product in products:
                if self._slugify_clean(product.name) == product_name:
                    return product
            
            return self.env['product.template']
            
        except Exception as e:
            _logger.warning("Error finding product by clean name %s: %s", product_name, str(e))
            return self.env['product.template']
    
    def _find_blog_by_clean_name(self, blog_name):
        """Find blog by clean name
        
        Args:
            blog_name (str): Clean blog name from URL
            
        Returns:
            recordset: Found blog or empty recordset
        """
        try:
            Blog = self.env['blog.blog']
            name_pattern = blog_name.replace('-', ' ')
            
            blogs = Blog.search([('name', 'ilike', name_pattern)])
            for blog in blogs:
                if self._slugify_clean(blog.name) == blog_name:
                    return blog
            
            return self.env['blog.blog']
            
        except Exception as e:
            _logger.warning("Error finding blog by clean name %s: %s", blog_name, str(e))
            return self.env['blog.blog']
    
    def _find_post_by_clean_name(self, blog, post_name):
        """Find blog post by clean name
        
        Args:
            blog: Blog record
            post_name (str): Clean post name from URL
            
        Returns:
            recordset: Found post or empty recordset
        """
        try:
            Post = self.env['blog.post']
            name_pattern = post_name.replace('-', ' ')
            
            posts = Post.search([
                ('blog_id', '=', blog.id),
                ('name', 'ilike', name_pattern),
                ('website_published', '=', True)
            ])
            
            for post in posts:
                if self._slugify_clean(post.name) == post_name:
                    return post
            
            return self.env['blog.post']
            
        except Exception as e:
            _logger.warning("Error finding post by clean name %s: %s", post_name, str(e))
            return self.env['blog.post']
