{
    'name': 'Modula URL Routing',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Clean URL routing without IDs for SEO optimization',
    'description': '''
        Transform Odoo website URLs from ID-based slugs to clean, SEO-friendly URLs.
        
        Features:
        - Clean category URLs: /sofas/modular-sofas
        - Clean product URLs: /loft-2-seater  
        - Clean blog URLs: /blog/living-journal/grand-opening
        - Custom page URLs: /warranty-claim-form
        - Automatic 301 redirects from old URLs
        - SEO optimization and performance monitoring
        
        This module only changes the URL display in browser address bar.
        All existing page rendering functionality remains unchanged.
    ''',
    'author': 'Carpet Call',
    'website': 'https://carpetcall.com.au/',
    'depends': [
        # Core dependencies
        'website',
        'website_sale',
        'website_blog',
        'http_routing',
        
        # Custom dependencies
        'modula_web',
        'modula_product',
    ],
    'data': [
        # Security
        'security/ir.model.access.csv',
        
        # Views
        'views/url_mapping_views.xml',
    ],
    'demo': [
        'demo/demo_url_mappings.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'modula_url_routing/static/src/js/url_tracking.js',
            'modula_url_routing/static/src/css/url_routing.css',
        ],
        'web.assets_backend': [
            'modula_url_routing/static/src/js/url_mapping_widget.js',
            'modula_url_routing/static/src/xml/url_mapping_widget.xml',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
    'post_init_hook': 'post_init_hook',
    'uninstall_hook': 'uninstall_hook',
}
