import { FormController } from "@web/views/form/form_controller";
import { pickUseConnectedEmployee } from "../employee_selection/employee_hooks";
import { useService } from "@web/core/utils/hooks";
import {
    onWillStart,
    useSubEnv,
} from "@odoo/owl";

export class SaleFormController extends FormController {
    setup() {
        super.setup();
        console.log("Sale Form Controller setup");

        this.actionService = useService('action');
        this.notification = useService('notification');
        onWillStart(async () => {
            // must check to know if employee already login
            var emp_login = false
            const order_action = await this.actionService.loadAction('sale.action_orders');
            const quotation_action = await this.actionService.loadAction('sale.action_quotations_with_onboarding');
            // await this.useEmployee.getAllEmployees();
            await this.useEmployee.getConnectedEmployees();
            if (([order_action.id, quotation_action.id].includes(this.env.config.actionId) && !this.props.resId)) {
                this.useEmployee.popupAddEmployee('add_employee');
            }
        });
        useSubEnv({
            model: this.model,
            reload: async () => {
                await this.model.load();
                await this.useEmployee.getConnectedEmployees();
            },
        });
        this.useEmployee = pickUseConnectedEmployee("form", this.props.context, this.env);

        this.useEmployee.setFormSaveCallbacks({
            refreshForm: this.refreshForm.bind(this),
            getSaleOrderId: () => this.props.resId
        });
    }
    async create() {
        if (this.props.resModel === 'sale.order') {
            this.useEmployee.popupAddEmployee('add_employee');
        }
        super.create();
    }
    async refreshForm() {
        try {
            this.render();

        } catch (error) {
            this.notification.add("Error refreshing form", { type: "danger" });
            console.error("Form refresh error:", error);
        }
    }
}
