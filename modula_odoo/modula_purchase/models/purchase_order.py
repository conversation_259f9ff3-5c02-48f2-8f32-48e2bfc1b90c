# -*- coding: utf-8 -*-

from odoo import _, api, fields, models
from odoo.exceptions import UserError


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    container_id = fields.Many2one(
        "container.container", string="Container", copy=False
    )
    container_count = fields.Integer(
        string="Container Count", compute="_compute_container_count"
    )
    is_mto = fields.Boolean(string="Is MTO", compute="_compute_is_mto")
    is_sent = fields.Boolean(string="Is Sent", default=False)
    so_status = fields.Char(
        string="SO Status", compute="_compute_so_status", compute_sudo=True
    )
    sale_order_id = fields.Many2one(
        "sale.order", string="Sale Order", compute="_compute_sale_order_id"
    )
    so_state = fields.Selection(
        string="Sales Order Status", related="sale_order_id.state",
    )

    def _compute_sale_order_id(self):
        for rec in self:
            rec.sale_order_id = rec._get_sale_orders()

    def _compute_is_mto(self):
        for rec in self:
            rec.is_mto = (
                rec._get_sale_orders()
                and "cancel" not in rec._get_sale_orders().mapped("state")
            )

    def _compute_container_count(self):
        for rec in self:
            rec.container_count = len(rec.container_id)

    def action_open_container(self):
        self.ensure_one()
        # add default move_ids to the context
        context = dict(self._context)
        # context.update({'default_move_ids': [(6,0,[self.id])]})
        # need to hide the moves if the user is not from HOLDING
        context.update(
            {
                "hide_move": True,
                "default_po_id": self.id,
            }
        )
        if self.container_id:
            return {
                "name": "Container",
                "type": "ir.actions.act_window",
                "view_mode": "form",
                "res_model": "container.container",
                "res_id": self.container_id.id,
                "context": context,
            }
        return False

    def button_confirm(self):
        for rec in self:
            if rec.is_mto:
                so_ids = rec._get_sale_orders()
                if so_ids:
                    if any(
                        so_id.remaining_amount / so_id.amount_total > 0.6
                        for so_id in so_ids.filtered(lambda so: so.amount_total > 0)
                    ):
                        raise UserError(
                            _(
                                "You cannot confirm a purchase order with a sale order that has not paid at least 40% of the amount."
                            )
                        )
                    if all(so_id.is_rewrite for so_id in so_ids):
                        raise UserError(
                            _("You cannot confirm a purchase order with sale orders in rewrite process.")
                        )
                    if so_ids.downpayment_ids.filtered(lambda payment: payment.state in ["in_process"]):
                        raise UserError(
                            _("You cannot confirm a purchase order with sale orders that have downpayment in process.")
                        )
            if any(rec.order_line.filtered(lambda l: l.product_qty == 0)):
                raise UserError(
                    _(
                        "You cannot confirm a purchase order with a line with 0 quantity."
                    )
                )
            else:
                super(PurchaseOrder, rec).button_confirm()
        return True

    @api.depends("order_line.sale_order_id")
    def _compute_sale_order_count(self):
        super()._compute_sale_order_count()
        for order in self:
            order_ids = self._get_sale_orders()
            so_name = order.origin.split("/")[0] if order.origin else False
            if not so_name:
                continue
            if not order_ids:
                order_ids = self.env["sale.order"].search([("name", "=", so_name)])
            else:
                if so_name:
                    additional_so = self.env["sale.order"].search(
                        [("name", "=", so_name)]
                    )
                    order_ids |= additional_so
            order.sale_order_count = len(order_ids)

    def action_view_sale_orders(self):
        self.ensure_one()
        sale_order_ids = self._get_sale_orders()
        so_name = self.origin.split("/")[0] if self.origin else False

        if so_name:
            if not sale_order_ids:
                sale_order_ids = self.env["sale.order"].search([("name", "=", so_name)])
            else:
                additional_po = self.env["sale.order"].search([("name", "=", so_name)])
                sale_order_ids |= additional_po
        sale_order_ids = sale_order_ids.ids
        if not sale_order_ids:
            so_name = self.origin.split("/")[0] if self.origin else False
            if so_name:
                so = self.env["sale.order"].search([("name", "=", so_name)])
                sale_order_ids = so.ids
        action = {
            "res_model": "purchase.order",
            "type": "ir.actions.act_window",
        }
        action = {
            "res_model": "sale.order",
            "type": "ir.actions.act_window",
        }
        if len(sale_order_ids) == 1:
            action.update(
                {
                    "view_mode": "form",
                    "res_id": sale_order_ids[0],
                }
            )
        else:
            action.update(
                {
                    "name": _("Sources Sale Orders %s", self.name),
                    "domain": [("id", "in", sale_order_ids)],
                    "view_mode": "list,form",
                }
            )
        return action

    @api.onchange("partner_id", "company_id")
    def onchange_partner_id(self):
        # force the currency to AUD
        self = self.with_company(self.company_id)
        aud_currency = self.env.ref("base.AUD")
        self.currency_id = aud_currency.id
        return {}

    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        return res

    def auto_send_rfq(self):
        template_id = self.env.ref("purchase.email_template_edi_purchase")
        if self:
            no_partner_po = self.filtered(lambda r: not r.partner_id.email)
            if no_partner_po:
                raise UserError(
                    _(
                        "You cannot auto send email to a partner without an email address. Please check partner configuration. Order(s): %s"
                        % no_partner_po.mapped("name")
                    )
                )
        for rec in self:
            template_id.send_mail(rec.id)
        self.write(
            {
                "is_sent": True,
            }
        )
