from odoo import models, fields, api

class ProductPricelist(models.Model):
    _inherit = "product.pricelist"

    def _get_product_price_with_tax(self, product, quantity, currency):
        price = super()._get_product_price(product, quantity, currency)
        price_inc_tax = product.taxes_id.compute_all(
            price,
            currency,
            1,
            product,
        )["total_included"]
        return price_inc_tax
