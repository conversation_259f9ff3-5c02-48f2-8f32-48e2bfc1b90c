from odoo import _, api, fields, models, tools
from odoo.exceptions import ValidationError
from odoo.tools import format_amount, format_datetime, formatLang


class ProductPricelist(models.Model):
    _inherit = "product.pricelist"

    def _get_product_price_with_tax(self, product, quantity, currency):
        price = super()._get_product_price(product, quantity, currency)
        price_inc_tax = product.taxes_id.compute_all(
            price,
            currency,
            1,
            product,
        )["total_included"]
        return price_inc_tax


class PricelistItem(models.Model):
    _inherit = "product.pricelist.item"

    base = fields.Selection(
        selection=[
            ("list_price", "Sales Price"),
            ("standard_price", "Cost"),
            ("tax_included", "Tax Included"),
            ("pricelist", "Other Pricelist"),
        ],
        string="Based on",
        default="list_price",
        required=True,
        help="Base price for computation.\n"
        "Sales Price: The base price will be the Sales Price.\n"
        "Cost Price: The base price will be the cost price.\n"
        "Tax Included: The base price will be the price including tax.\n"
        "Other Pricelist: Computation of the base price based on another Pricelist.",
    )

    def _compute_base_price(self, product, quantity, uom, date, currency):
        """Compute the base price for a given rule

        :param product: recordset of product (product.product/product.template)
        :param float qty: quantity of products requested (in given uom)
        :param uom: unit of measure (uom.uom record)
        :param datetime date: date to use for price computation and currency conversions
        :param currency: currency in which the returned price must be expressed

        :returns: base price, expressed in provided pricelist currency
        :rtype: float
        """
        currency.ensure_one()

        rule_base = self.base or "list_price"
        if rule_base == "tax_included":
            src_currency = product.currency_id
            price = product._price_compute("list_price", uom=uom, date=date)[product.id]
        elif rule_base == "pricelist" and self.base_pricelist_id:
            price = self.base_pricelist_id._get_product_price(
                product,
                quantity,
                currency=self.base_pricelist_id.currency_id,
                uom=uom,
                date=date,
            )
            src_currency = self.base_pricelist_id.currency_id
        elif rule_base == "standard_price":
            src_currency = product.cost_currency_id
            price = product._price_compute(rule_base, uom=uom, date=date)[product.id]
        else:  # list_price
            src_currency = product.currency_id
            price = product._price_compute(rule_base, uom=uom, date=date)[product.id]

        if src_currency != currency:
            price = src_currency._convert(
                price, currency, self.env.company, date, round=False
            )

        return price

    def _compute_price_label(self):
        for item in self:
            if item.compute_price == "fixed":
                item.price = formatLang(
                    item.env,
                    item.fixed_price,
                    dp="Product Price",
                    currency_obj=item.currency_id,
                )
            elif item.compute_price == "percentage":
                percentage = self._get_integer(item.percent_price)
                if item.base_pricelist_id:
                    item.price = _(
                        "%(percentage)s %% discount on %(pricelist)s",
                        percentage=percentage,
                        pricelist=item.base_pricelist_id.display_name,
                    )
                else:
                    item.price = _(
                        "%(percentage)s %% discount on sales price",
                        percentage=percentage,
                    )
            else:
                base_str = ""
                if item.base == "pricelist" and item.base_pricelist_id:
                    base_str = item.base_pricelist_id.display_name
                elif item.base == "standard_price":
                    base_str = _("product cost")
                elif item.base == "tax_included":
                    base_str = _("sales price (incl. tax)")
                else:
                    base_str = _("sales price")

                extra_fee_str = ""
                if item.price_surcharge > 0:
                    extra_fee_str = _(
                        "+ %(amount)s extra fee",
                        amount=format_amount(
                            item.env,
                            abs(item.price_surcharge),
                            currency=item.currency_id,
                        ),
                    )
                elif item.price_surcharge < 0:
                    extra_fee_str = _(
                        "- %(amount)s rebate",
                        amount=format_amount(
                            item.env,
                            abs(item.price_surcharge),
                            currency=item.currency_id,
                        ),
                    )
                discount_type, percentage = self._get_displayed_discount(item)
                item.price = _(
                    "%(percentage)s %% %(discount_type)s on %(base)s %(extra)s",
                    percentage=percentage,
                    discount_type=discount_type,
                    base=base_str,
                    extra=extra_fee_str,
                )

    def _compute_rule_tip(self):
        base_selection_vals = {
            elem[0]: elem[1]
            for elem in self._fields["base"]._description_selection(self.env)
        }
        self.rule_tip = False
        for item in self:
            if item.compute_price != "formula":
                continue
            base_amount = 100
            discount = (
                item.price_discount
                if item.base != "standard_price"
                else -item.price_markup
            )
            discount_factor = (100 - discount) / 100
            discounted_price = base_amount * discount_factor
            if item.price_round:
                discounted_price = tools.float_round(
                    discounted_price, precision_rounding=item.price_round
                )
            surcharge = tools.format_amount(
                item.env, item.price_surcharge, item.currency_id
            )
            discount_type, discount = self._get_displayed_discount(item)
            base_label = base_selection_vals[item.base]
            if item.base == "tax_included":
                base_label = _("sales price (incl. tax)")
            item.rule_tip = _(
                "%(base)s with a %(discount)s %% %(discount_type)s and %(surcharge)s extra fee\n"
                "Example: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s",
                base=base_label,
                discount=discount,
                discount_type=discount_type,
                surcharge=surcharge,
                amount=tools.format_amount(item.env, 100, item.currency_id),
                discount_charge=discount_factor,
                price_surcharge=surcharge,
                total_amount=tools.format_amount(
                    item.env, discounted_price + item.price_surcharge, item.currency_id
                ),
            )
