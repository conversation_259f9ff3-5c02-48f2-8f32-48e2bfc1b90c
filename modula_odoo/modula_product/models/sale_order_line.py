# -*- coding: utf-8 -*-
from odoo import api, fields, models, tools


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    is_configurable_sku = fields.Boolean(
        string="Is the product configurable?",
        related="product_template_id.is_range",
    )

    @api.depends("product_id", "product_uom", "product_uom_qty")
    def _compute_price_unit(self):
        super()._compute_price_unit()
        for line in self:
            if not line.order_id:
                continue
            # check if the price has been manually set or there is already invoiced amount.
            # if so, the price shouldn't change as it might have been manually edited.
            if (
                (
                    line.technical_price_unit != line.price_unit
                    and not line.env.context.get("force_price_recomputation")
                )
                or line.qty_invoiced > 0
                or (line.product_id.expense_policy == "cost" and line.is_expense)
            ):
                continue
            line = line.with_context(sale_write_from_compute=True)
            if not line.product_uom or not line.product_id:
                line.price_unit = 0.0
                line.technical_price_unit = 0.0
            else:
                # Don't compute the price for deleted lines.
                (
                    pricelist_price,
                    pricelist_rule_id,
                ) = line.order_id.pricelist_id._get_product_price_rule(
                    product=line.product_id,
                    quantity=line.product_uom_qty or 1.0,
                    target_currency=line.currency_id,
                )
                pricelist_item = self.env["product.pricelist.item"].browse(
                    pricelist_rule_id
                )

                if (
                    pricelist_item
                    and pricelist_item.base == "tax_included"
                    and line.price_unit > 0
                ):
                    line.price_unit = line.product_id.list_price

    @api.depends("product_uom_qty", "discount", "price_unit", "tax_id")
    def _compute_amount(self):
        super()._compute_amount()
        for line in self:
            if line.product_id and line.product_uom_qty > 0 and line.currency_id:
                (
                    pricelist_price,
                    pricelist_rule_id,
                ) = line.order_id.pricelist_id._get_product_price_rule(
                    product=line.product_id,
                    quantity=line.product_uom_qty or 1.0,
                    target_currency=line.currency_id,
                )
                pricelist_item = self.env["product.pricelist.item"].browse(
                    pricelist_rule_id
                )

                if (
                    pricelist_item
                    and pricelist_item.base == "tax_included"
                    and line.price_total > 0
                ):
                    line.price_total = tools.float_round(
                        line.price_total, precision_rounding=pricelist_item.price_round
                    )
