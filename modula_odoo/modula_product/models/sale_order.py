# -*- coding: utf-8 -*-
from odoo import api, fields, models, tools


class SaleOrder(models.Model):
    _inherit = "sale.order"

    # @api.depends('order_line.price_subtotal', 'currency_id', 'company_id', 'payment_term_id')
    # def _compute_amounts(self):
    #     super()._compute_amounts()
    #     for order in self:
    #         if order.amount_total > 0 and order.currency_id:
    #             (
    #                 pricelist_price,
    #                 pricelist_rule_id,
    #             ) = order.pricelist_id._get_product_price_rule(
    #                 product=order.product_id,
    #                 quantity=order.product_uom_qty or 1.0,
    #                 target_currency=order.currency_id,
    #             )
    #             pricelist_item = self.env["product.pricelist.item"].browse(
    #                 pricelist_rule_id
    #             )

    #             if (
    #                 pricelist_item
    #                 and pricelist_item.base == "tax_included"
    #                 and line.price_total > 0
    #             ):
    #                 line.price_total = tools.float_round(
    #                     line.price_total, precision_rounding=pricelist_item.price_round
    #                 )

    @api.depends_context("lang")
    @api.depends(
        "order_line.price_subtotal",
        "order_line.amount_rounded",
        "currency_id",
        "company_id",
        "payment_term_id",
    )
    def _compute_tax_totals(self):
        super()._compute_tax_totals()
        for order in self:
            order_lines = order.order_line.filtered(lambda x: not x.display_type)
            # Ghi đè các trường tổng bằng tổng các dòng đã làm tròn
            order_line = order_lines and order_lines[0] or False
            if order_line:
                order.tax_totals["total_amount"] = order_line.get_amount_rounded(
                    order.tax_totals["total_amount"]
                )
