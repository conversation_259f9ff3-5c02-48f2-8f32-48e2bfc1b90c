from odoo import http, _
from odoo.http import request

from odoo.addons.website_sale.controllers.main import WebsiteSale
from odoo.addons.account.controllers.terms import TermsController
from odoo.addons.website_helpdesk.controllers.main import WebsiteHelpdesk
from odoo.addons.website_sale_autocomplete.controllers.main import AutoCompleteController
from odoo.addons.portal.controllers.portal import CustomerPortal
from odoo.http import request
from odoo.tools import lazy
import json


def sitemap_terms(env, rule, qs):
    if qs and qs.lower() not in '/terms':
        return
    use_invoice_terms = env['ir.config_parameter'].sudo().get_param('account.use_invoice_terms')
    if use_invoice_terms and env.company.terms_type == 'html':
        yield {'loc': '/terms'}


class AutoCompleteControllerAU(AutoCompleteController):

    def _perform_place_search(self, partial_address, api_key=None, session_id=None, language_code=None,
                              country_code='AU'):
        # Override to set default country_code to 'AU'
        return super()._perform_place_search(
            partial_address,
            api_key=api_key,
            session_id=session_id,
            language_code=language_code,
            country_code=country_code
        )


class CustomTermsController(TermsController):

    @http.route('/terms', type='http', auth='public', website=True, sitemap=sitemap_terms)
    def terms_conditions(self, **kwargs):
        use_invoice_terms = request.env['ir.config_parameter'].sudo().get_param('account.use_invoice_terms')
        if not (use_invoice_terms and request.env.company.terms_type == 'html'):
            return request.render('http_routing.http_error', {
                'status_code': _('Oops'),
                'status_message': _("""The requested page is invalid, or doesn't exist anymore.""")})
        values = {
            'use_invoice_terms': use_invoice_terms,
            'company': request.env.company,
            'content': request.env['terms.conditions'].sudo().search(
                [('name', '=', 'Terms & Conditions')],
                limit=1)
        }
        return request.render("modula_web.terms_and_conditions", values)


class CustomWebsiteHelpdesk(WebsiteHelpdesk):
    @http.route(['/helpdesk', '/helpdesk/<model("helpdesk.team"):team>'], type='http', auth="public", website=True,
                sitemap=True)
    def website_helpdesk_teams(self, team=None, **kwargs):
        # Call the original controller to get the result (template + context)
        result = super(CustomWebsiteHelpdesk, self).website_helpdesk_teams(team=team, **kwargs)

        # Only extend if the result is rendering a template (not a redirect)
        if isinstance(result, http.Response) and hasattr(result, 'qcontext'):
            countries = request.env['res.country'].sudo().search([])
            country = request.env['res.country'].sudo().search([('code', '=', 'AU')], limit=1)
            country_states = request.env['res.country.state'].sudo().search([('country_id', '=', country.id)])

            # Inject your extra context
            result.qcontext.update({
                'country': country,
                'countries': countries,
                'country_states': country_states,
                'state_id': None,
            })

            # if team and team.website_slug != 'support':
            #     team = team.sudo()
            #     team.website_slug = 'support'

        return result


class CustomerPortalInherit(CustomerPortal):

    # @http.route('/my/account', type='http', auth='user', website=True, methods=['GET', 'POST'], )
    # def my_details(self, redirect=None, **post):
    #     response = super().my_details(redirect=redirect, **post)
    #     partner = request.env.user.partner_id
    #     # Set Australia as default country
    #     country = request.env['res.country'].sudo().search([('code', '=', 'AU')], limit=1)
    #     country_states = request.env['res.country.state'].sudo().search([('country_id', '=', country.id)])
    #
    #     response.qcontext.update({
    #         'country_states': country_states,
    #     })
    #     return response

    def _inject_states(self, response):
        # countries = request.env['res.country'].sudo().search([])
        country = request.env['res.country'].sudo().search([('code', '=', 'AU')], limit=1)
        country_states = request.env['res.country.state'].sudo().search([('country_id', '=', country.id)])
        print("Country States:", country_states)
        response.qcontext.update({'country_states': country_states})
    #
    # @http.route('/my/account', type='http', auth='user', website=True, methods=['POST'])
    # def my_details_post(self, redirect=None, **post):
    #     response = super().my_details(redirect=redirect, **post)
    #     self._inject_states(response)
    #     return response
    #
    # @http.route('/my/account', type='http', auth='user', website=True, methods=['GET'])
    # def my_details_get(self, redirect=None, **post):
    #     print("=========================ADDRESS================")
    #     response = super().my_details(redirect=redirect, **post)
    #     self._inject_states(response)
    #     return response


class PageRoutings(http.Controller):
    @http.route('/showroom', type='http', auth='public', website=True)
    def showroom_page(self, **kw):
        return request.render('modula_web.website_showroom', {})

    @http.route('/our-story', type='http', auth='public', website=True)
    def our_story_page(self, **kw):
        return request.render('modula_web.website_our_story', {})

    @http.route('/contactus', type='http', auth='public', website=True, methods=['GET'])
    def contact_us_page(self, **kw):
        company = request.env.company
        return request.render('modula_web.website_contact_us', {'company': company})

    # @http.route('/warranty-claim', type='http', auth='public', website=True)
    # def warranty_claim_page(self, **kw):
    #     # return request.render('modula_web.website_warranty_claim_form', {})
    #
    #     # Fetch the country record for Australia
    #     countries = request.env['res.country'].sudo().search([('code', '=', 'AU')], limit=1)
    #     # countries = request.env['res.country'].sudo().search([])
    #     country = request.env['res.country'].sudo().search([('code', '=', 'AU')], limit=1)
    #
    #     # Fetch all states associated with Australia
    #     country_states = request.env['res.country.state'].sudo().search([('country_id', '=', country.id)])
    #     # country_states = request.env['res.country.state'].sudo().search([])
    #
    #     # Pass the data to the template
    #     return request.render('modula_web.helpdesk_team_extended', {
    #         'country': country,
    #         'countries': countries,
    #         'country_states': country_states,
    #         'state_id': None,
    #     })

    @http.route('/shipping-and-delivery', type='http', auth='public', website=True)
    def shipping_and_delivery_page(self, **kw):
        return request.render('modula_web.shipping_and_delivery', {})

    @http.route('/returns-and-refunds', type='http', auth='public', website=True)
    def returns_and_refunds_page(self, **kw):
        return request.render('modula_web.returns_and_refunds', {})

    @http.route('/privacy-policy', type='http', auth='public', website=True)
    def privacy_policy_page(self, **kw):
        values = {
            'company': request.env.company,
            'content': request.env['terms.conditions'].sudo().search(
                [('name', '=', 'Privacy Policy')],
                limit=1)
        }
        return request.render('modula_web.privacy_policy', values)

    # @http.route(['/terms-and-conditions'], type='http', auth='public', website=True)
    # def terms_and_conditions_page(self, **kw):
    #     return request.render('modula_web.terms_and_conditions', {})

    @http.route('/care-and-maintenance', type='http', auth='public', website=True)
    def care_and_maintenance_page(self, **kw):
        return request.render('modula_web.care_and_maintenance', {})


def getProductColorAttributes(product):
    color_attrs = []

    for line in product.attribute_line_ids:
        if line.attribute_id.display_type in ('color', 'colour'):
            values = line.value_ids
            color_attrs.append({
                'attribute': line.attribute_id.name,
                'values': [v.name for v in line.value_ids],
                'count': len(values),
            })

    return color_attrs


def getMouseoverImage(product):
    """
        Retrieve the first image URL from product.image.entry records
        with config_id.name == 'mouseover' for the given product.
        """

    entries = request.env['product.image'].sudo().search([
        ('entry_product_tmpl_id', '=', product.id),
        ('product_image_entry_id.config_id.name', 'ilike', 'mouseover'),
        ('product_image_entry_id.image_ids', '!=', False)
    ], limit=1)

    if entries:
        # Get the first image attachment
        image = entries[0]
        # Construct the image URL
        image_url = f'/web/image/product.image/{image.id}/image_1024'
        return image_url

    return None


def getProductDimensionImage(product):
    """
        Retrieve the first image URL from product.image.entry records
        with config_id.name == 'mouseover' for the given product.
        """

    entries = request.env['product.image'].sudo().search([
        ('entry_product_tmpl_id', '=', product.id),
        ('product_image_entry_id.config_id.name', 'ilike', 'dimensions'),
        ('product_image_entry_id.image_ids', '!=', False)
    ], limit=1)

    # print(entries)

    if entries:
        # Get the first image attachment
        image = entries[0]
        # Construct the image URL
        image_url = f'/web/image/product.image/{image.id}/image_1024'
        return image_url

    return None


# def _get_combination_from_attribute_value_ids(self, product, value_ids):
#     return request.env['product.product'].search([
#         ('product_tmpl_id', '=', product.id),
#         ('product_template_attribute_value_ids.product_attribute_value_id', 'in', value_ids),
#     ], limit=1)
#     # return self.env['product.template.attribute.value'].browse(value_ids)


class CustomWebsiteSale(WebsiteSale):

    @http.route('/shop/wishlist', type='http', auth='public', website=True, sitemap=False)
    def get_wishlist(self, count=False, **kw):
        wishes = request.env['product.wishlist'].with_context(display_default_code=False).current()
        if count:
            return request.make_response(json.dumps(wishes.product_id.ids))

        # if not wishes:
        #     return request.redirect('/shop')

        return request.render(
            'website_sale_wishlist.product_wishlist',
            {
                'wishes': wishes,
            }
        )

    @http.route([
        '/shop',
        '/shop/page/<int:page>',
        '/shop/category/<model("product.public.category"):category>',
        '/shop/category/<model("product.public.category"):category>/page/<int:page>',
    ], type='http', auth="public", website=True, sitemap=WebsiteSale.sitemap_shop)
    def shop(self, page=0, category=None, search='', min_price=0.0, max_price=0.0, ppg=False, **post):
        # Check if the module is installed
        is_wishlist_module_installed = request.env['ir.module.module'].sudo().search([
            ('name', '=', 'website_sale_wishlist'),
            ('state', '=', 'installed')
        ], limit=1)

        # Call the parent method and get the response
        response = super(CustomWebsiteSale, self).shop(page, category, search, min_price, max_price, ppg, **post)

        # Add the 'is_module_installed' variable to the template context
        response.qcontext['is_wishlist_module_installed'] = bool(is_wishlist_module_installed)
        response.qcontext['get_color_attributes'] = lambda product: lazy(
            lambda: getProductColorAttributes(product))
        response.qcontext['get_mouseover_product_image'] = lambda product: lazy(
            lambda: getMouseoverImage(product))
        # response.qcontext['cheapest_variant_price'] = lambda product: lazy(
        #     lambda: compute_cheapest_variant(product, category)
        # )

        return response

    @http.route(['/shop/<model("product.template"):product>'], type='http', auth="public", website=True,
                sitemap=WebsiteSale.sitemap_products, readonly=True)
    def product(self, product, category='', search='', **kwargs):
        if not request.website.has_ecommerce_access():
            return request.redirect('/web/login')

        # Handle query params combination if present
        combination_query_param = kwargs.get('combination')
        pre_combination_info = None

        if combination_query_param:
            try:
                # Convert param string to list of int IDs
                value_ids = [int(v) for v in combination_query_param.split(',') if v.isdigit()]

                # Fetch product.template.attribute.value records
                combination = request.env['product.template.attribute.value'].browse(value_ids)

                if combination:
                    # Use sale_get_combination_info (same as JS) to get full combination data
                    pre_combination_info = product._get_combination_info(
                        combination=combination,
                        add_qty=1,
                        product_id=product.id
                    )

            except Exception as e:
                request.env['ir.logging'].sudo().create({
                    'name': 'Combination Info Error',
                    'type': 'server',
                    'level': 'WARNING',
                    'message': f'Failed to compute combination info: {str(e)}',
                    'path': 'website_sale_variant_optimized',
                    'line': 'product',
                    'func': 'product',
                })

            # Call the default product page rendering
        response = super().product(product, category, search, **kwargs)

        # Inject the cheapest combination data if found
        if pre_combination_info:
            response.qcontext['new_combination_info'] = pre_combination_info
            response.qcontext['product_variant'] = request.env['product.product'].browse(
                pre_combination_info['product_id']
            )
            response.qcontext['combination_indices'] = combination_query_param
            response.qcontext['combination_indices_second_part'] = int(
                combination_query_param.split(',')[1]) if ',' in combination_query_param else int(0)
            response.qcontext['combination_preloaded'] = True

        # Inject product dimensions
        response.qcontext['get_product_dimension'] = lambda product: lazy(
            lambda: getProductDimensionImage(product))

        return response
