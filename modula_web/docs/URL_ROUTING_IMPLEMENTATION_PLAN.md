# Odoo 18 URL Routing Implementation Plan

## 🎯 **Project Overview**

**Objective**: Transform Odoo website URLs from ID-based slugs to clean, SEO-friendly URLs without IDs.

### **Current vs Target URL Structure**

| Page Type | Current Format | Target Format |
|-----------|---------------|---------------|
| Category | `/shop/category/sofas-modular-sofas-31` | `/sofas/modular-sofas` |
| Product | `/shop/loft-2-seater-1377` | `/loft-2-seater` |
| Product with Attributes | `/shop/loft-2-seater-1377#attribute_values=1,4` | `/loft-2-seater#attribute_values=sassari-light-grey` |
| Blog | `/blog/modula-living-journal-1` | `/blog/modula-living-journal` |
| Blog Post | `/blog/modula-living-journal-1/our-grand-opening-1` | `/blog/modula-living-journal/our-grand-opening` |
| Helpdesk | `/helpdesk/warranty-claim-2` | `/warranty-claim-form` |

## 🏗️ **Implementation Strategy**

### **Phase 1: Foundation Module Creation**
Create `modula_url_routing` module with:
- Custom URL routing controllers
- URL generation overrides
- Redirect management system
- SEO optimization features

### **Phase 2: URL Pattern Implementation**
1. **Category URLs**: `/{parent_category}/{child_category}`
2. **Product URLs**: `/{product_name}`
3. **Blog URLs**: `/blog/{blog_name}/{post_name}`
4. **Custom Page URLs**: Direct mapping (e.g., `/warranty-claim-form`)

### **Phase 3: Backward Compatibility**
- Automatic 301 redirects from old to new URLs
- URL mapping database
- Migration scripts for existing URLs

## 📋 **Technical Implementation Details**

### **1. Module Structure**
```
modula_url_routing/
├── __manifest__.py
├── controllers/
│   ├── __init__.py
│   ├── category_routing.py
│   ├── product_routing.py
│   ├── blog_routing.py
│   └── redirect_controller.py
├── models/
│   ├── __init__.py
│   ├── ir_http.py
│   ├── product_public_category.py
│   ├── product_template.py
│   ├── blog_blog.py
│   ├── blog_post.py
│   └── url_mapping.py
├── data/
│   └── url_redirects.xml
└── docs/
    └── implementation_guide.md
```

### **2. Core Components**

#### **A. Custom Slug Generation**
```python
# Override in ir.http model
@classmethod
def _slug_clean(cls, value):
    """Generate clean slug without ID"""
    try:
        name = value.display_name if hasattr(value, 'display_name') else str(value)
        return cls._slugify(name)
    except AttributeError:
        return cls._slugify(str(value))
```

#### **B. URL Mapping Model**
```python
class UrlMapping(models.Model):
    _name = 'url.mapping'
    _description = 'URL Mapping for Clean URLs'
    
    old_url = fields.Char('Old URL', required=True, index=True)
    new_url = fields.Char('New URL', required=True, index=True)
    model_name = fields.Char('Model Name')
    record_id = fields.Integer('Record ID')
    redirect_type = fields.Selection([
        ('301', '301 Permanent'),
        ('302', '302 Temporary')
    ], default='301')
```

#### **C. Category URL Controller**
```python
@http.route([
    '/<string:parent_category>/<string:child_category>',
    '/<string:parent_category>/<string:child_category>/page/<int:page>',
], type='http', auth="public", website=True, sitemap=True)
def category_clean_url(self, parent_category, child_category, page=1, **kwargs):
    """Handle clean category URLs"""
    # Find category by clean URL structure
    category = self._find_category_by_clean_url(parent_category, child_category)
    if not category:
        raise werkzeug.exceptions.NotFound()
    
    # Redirect to shop controller with category
    return request.redirect(f'/shop/category/{category.id}', code=301)
```

### **3. URL Generation Override**

#### **A. Product Template URL**
```python
class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    def _compute_website_url(self):
        """Override to generate clean URLs"""
        super()._compute_website_url()
        for product in self:
            if product.id:
                clean_name = self.env['ir.http']._slugify(product.name)
                product.website_url = f"/{clean_name}"
```

#### **B. Category URL Generation**
```python
class ProductPublicCategory(models.Model):
    _inherit = 'product.public.category'
    
    def _compute_website_url(self):
        """Generate clean category URLs"""
        for category in self:
            if category.parent_id:
                parent_slug = self.env['ir.http']._slugify(category.parent_id.name)
                child_slug = self.env['ir.http']._slugify(category.name)
                category.website_url = f"/{parent_slug}/{child_slug}"
            else:
                category.website_url = f"/{self.env['ir.http']._slugify(category.name)}"
```

## 🔄 **Migration Strategy**

### **1. URL Mapping Creation**
```python
def create_url_mappings(self):
    """Create mappings for existing URLs"""
    # Products
    products = self.env['product.template'].search([('website_published', '=', True)])
    for product in products:
        old_url = f"/shop/{self.env['ir.http']._slug(product)}"
        new_url = f"/{self.env['ir.http']._slugify(product.name)}"
        self.env['url.mapping'].create({
            'old_url': old_url,
            'new_url': new_url,
            'model_name': 'product.template',
            'record_id': product.id,
        })
```

### **2. Redirect Controller**
```python
@http.route('/shop/<path:old_path>', type='http', auth="public", website=True)
def handle_old_urls(self, old_path, **kwargs):
    """Handle redirects from old URLs to new clean URLs"""
    old_url = f"/shop/{old_path}"
    mapping = request.env['url.mapping'].search([('old_url', '=', old_url)], limit=1)
    
    if mapping:
        return request.redirect(mapping.new_url, code=301)
    
    # Fallback to original controller
    return super().handle_old_urls(old_path, **kwargs)
```

## 📊 **Implementation Phases**

### **Phase 1: Foundation (Week 1-2)**
- [ ] Create `modula_url_routing` module
- [ ] Implement URL mapping model
- [ ] Create basic redirect controller
- [ ] Set up clean slug generation

### **Phase 2: Category URLs (Week 3)**
- [ ] Implement category URL routing
- [ ] Override category URL generation
- [ ] Create category URL mappings
- [ ] Test category navigation

### **Phase 3: Product URLs (Week 4)**
- [ ] Implement product URL routing
- [ ] Override product URL generation
- [ ] Handle product attribute URLs
- [ ] Create product URL mappings

### **Phase 4: Blog URLs (Week 5)**
- [ ] Implement blog URL routing
- [ ] Override blog URL generation
- [ ] Create blog URL mappings
- [ ] Test blog navigation

### **Phase 5: Custom Pages (Week 6)**
- [ ] Implement custom page routing
- [ ] Create specific page mappings
- [ ] Test all custom pages

### **Phase 6: Testing & Optimization (Week 7-8)**
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] SEO validation
- [ ] Documentation completion

## ⚠️ **Critical Considerations**

### **1. Unique URL Constraints**
- Ensure product names are unique for clean URLs
- Handle duplicate names with suffixes
- Implement conflict resolution

### **2. SEO Impact**
- Implement proper 301 redirects
- Update sitemap generation
- Maintain canonical URLs

### **3. Performance**
- Cache URL mappings
- Optimize database queries
- Monitor redirect performance

### **4. Backward Compatibility**
- Maintain old URL functionality
- Gradual migration approach
- Fallback mechanisms

## 🧪 **Testing Strategy**

### **1. Unit Tests**
- URL generation tests
- Slug creation tests
- Mapping creation tests

### **2. Integration Tests**
- End-to-end navigation tests
- Redirect functionality tests
- SEO compliance tests

### **3. Performance Tests**
- Load testing with redirects
- Database query optimization
- Cache effectiveness tests

## 📈 **Success Metrics**

### **1. Technical Metrics**
- All URLs follow new clean format
- 301 redirects working correctly
- No broken links
- Performance maintained

### **2. SEO Metrics**
- Improved URL readability
- Better search engine indexing
- Maintained page rankings

### **3. User Experience**
- Cleaner, more memorable URLs
- Improved navigation experience
- Faster page loads

## 🔧 **Development Guidelines**

### **1. Follow Odoo 18 Patterns**
- Use proper inheritance patterns
- Follow controller best practices
- Implement proper error handling

### **2. Code Quality**
- Comprehensive documentation
- Unit test coverage
- Performance optimization

### **3. Deployment Strategy**
- Staged rollout approach
- Monitoring and rollback plans
- User communication strategy

## 🛠️ **Detailed Implementation Guide**

### **Step 1: Create Module Structure**

#### **A. Module Manifest**
```python
# modula_url_routing/__manifest__.py
{
    'name': 'Modula URL Routing',
    'version': '********.0',
    'category': 'Website',
    'description': 'Clean URL routing without IDs for SEO optimization',
    'author': 'Carpet Call',
    'website': 'https://carpetcall.com.au/',
    'depends': [
        'website',
        'website_sale',
        'website_blog',
        'modula_web',
        'modula_product'
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/url_redirects.xml',
        'views/url_mapping_views.xml',
    ],
    'installable': True,
    'application': False,
    'license': 'LGPL-3',
}
```

#### **B. Security Configuration**
```csv
# security/ir.model.access.csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_url_mapping_public,url.mapping.public,model_url_mapping,,1,0,0,0
access_url_mapping_user,url.mapping.user,model_url_mapping,base.group_user,1,1,1,1
```

### **Step 2: Core Models Implementation**

#### **A. URL Mapping Model**
```python
# models/url_mapping.py
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class UrlMapping(models.Model):
    _name = 'url.mapping'
    _description = 'URL Mapping for Clean URLs'
    _rec_name = 'new_url'

    old_url = fields.Char('Old URL', required=True, index=True)
    new_url = fields.Char('New URL', required=True, index=True)
    model_name = fields.Char('Model Name', required=True)
    record_id = fields.Integer('Record ID', required=True)
    redirect_type = fields.Selection([
        ('301', '301 Permanent'),
        ('302', '302 Temporary')
    ], default='301', required=True)
    active = fields.Boolean('Active', default=True)

    @api.constrains('new_url')
    def _check_unique_new_url(self):
        for record in self:
            existing = self.search([
                ('new_url', '=', record.new_url),
                ('id', '!=', record.id),
                ('active', '=', True)
            ])
            if existing:
                raise ValidationError(_('New URL must be unique: %s') % record.new_url)

    def find_by_old_url(self, old_url):
        """Find mapping by old URL"""
        return self.search([('old_url', '=', old_url), ('active', '=', True)], limit=1)

    def find_by_new_url(self, new_url):
        """Find mapping by new URL"""
        return self.search([('new_url', '=', new_url), ('active', '=', True)], limit=1)
```

#### **B. Enhanced ir.http Model**
```python
# models/ir_http.py
from odoo import models, api
import re

class IrHttp(models.AbstractModel):
    _inherit = 'ir.http'

    @classmethod
    def _slugify_clean(cls, name):
        """Generate clean slug without ID suffix"""
        if not name:
            return ''

        # Convert to lowercase and replace spaces/special chars with hyphens
        slug = re.sub(r'[^\w\s-]', '', name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')

    @classmethod
    def _generate_clean_url(cls, record, url_pattern=None):
        """Generate clean URL for any record"""
        if not record or not record.id:
            return ''

        if url_pattern:
            return url_pattern.format(record=record)

        # Default pattern: use slugified name
        clean_name = cls._slugify_clean(record.display_name)
        return f"/{clean_name}"

    def _find_record_by_clean_url(self, model_name, clean_url):
        """Find record by clean URL pattern"""
        # Extract name from URL
        url_parts = clean_url.strip('/').split('/')
        if not url_parts:
            return None

        # Search by name pattern
        Model = self.env[model_name]
        name_pattern = url_parts[-1].replace('-', ' ')

        # Try exact match first
        records = Model.search([('name', 'ilike', name_pattern)])
        if len(records) == 1:
            return records

        # Try fuzzy matching if multiple or no results
        for record in records:
            if self._slugify_clean(record.name) == url_parts[-1]:
                return record

        return None
```

### **Step 3: Controller Implementation**

#### **A. Category Routing Controller**
```python
# controllers/category_routing.py
from odoo import http
from odoo.http import request
import werkzeug

class CategoryRouting(http.Controller):

    @http.route([
        '/<string:parent_category>/<string:child_category>',
        '/<string:parent_category>/<string:child_category>/page/<int:page>',
    ], type='http', auth="public", website=True, sitemap=True)
    def category_clean_url(self, parent_category, child_category, page=1, **kwargs):
        """Handle clean category URLs like /sofas/modular-sofas"""

        # Find category by URL structure
        category = self._find_category_by_url_parts(parent_category, child_category)

        if not category:
            # Check URL mapping for redirects
            clean_url = f"/{parent_category}/{child_category}"
            mapping = request.env['url.mapping'].find_by_new_url(clean_url)
            if mapping:
                old_url = mapping.old_url
                return request.redirect(old_url, code=302)

            raise werkzeug.exceptions.NotFound()

        # Redirect to standard shop URL with proper parameters
        shop_url = f"/shop/category/{request.env['ir.http']._slug(category)}"
        if page > 1:
            shop_url += f"/page/{page}"

        # Preserve query parameters
        if kwargs:
            query_string = werkzeug.urls.url_encode(kwargs)
            shop_url += f"?{query_string}"

        return request.redirect(shop_url, code=301)

    def _find_category_by_url_parts(self, parent_name, child_name):
        """Find category by parent and child URL parts"""
        Category = request.env['product.public.category']

        # Convert URL parts back to names
        parent_pattern = parent_name.replace('-', ' ')
        child_pattern = child_name.replace('-', ' ')

        # Find parent category
        parent_categories = Category.search([
            ('parent_id', '=', False),
            ('name', 'ilike', parent_pattern)
        ])

        for parent in parent_categories:
            if request.env['ir.http']._slugify_clean(parent.name) == parent_name:
                # Find child category
                child_categories = Category.search([
                    ('parent_id', '=', parent.id),
                    ('name', 'ilike', child_pattern)
                ])

                for child in child_categories:
                    if request.env['ir.http']._slugify_clean(child.name) == child_name:
                        return child

        return None
```

---

**Next Steps**: Continue with Product and Blog routing implementation following this detailed guide.
