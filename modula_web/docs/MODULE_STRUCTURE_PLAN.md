# Module Structure Plan for URL Routing Implementation

## 🏗️ **Recommended Module Structure**

### **Option 1: Single Comprehensive Module (Recommended)**

```
modula_url_routing/
├── __init__.py
├── __manifest__.py
├── controllers/
│   ├── __init__.py
│   ├── category_routing.py      # Category clean URL handlers
│   ├── product_routing.py       # Product clean URL handlers  
│   ├── blog_routing.py          # Blog clean URL handlers
│   ├── custom_page_routing.py   # Custom page handlers
│   └── redirect_controller.py   # Fallback redirect handler
├── models/
│   ├── __init__.py
│   ├── ir_http.py              # Enhanced HTTP routing methods
│   ├── url_mapping.py          # URL mapping model
│   ├── product_template.py     # Product URL overrides
│   ├── product_public_category.py  # Category URL overrides
│   ├── blog_blog.py            # Blog URL overrides
│   └── blog_post.py            # Blog post URL overrides
├── data/
│   ├── url_redirects.xml       # Initial redirect data
│   └── create_url_mappings.py  # Migration script
├── security/
│   └── ir.model.access.csv     # Access rights
├── views/
│   ├── url_mapping_views.xml   # URL mapping management views
│   └── website_templates.xml   # Template overrides if needed
├── tests/
│   ├── __init__.py
│   ├── test_url_routing.py     # Unit tests
│   ├── test_url_integration.py # Integration tests
│   ├── test_url_performance.py # Performance tests
│   └── test_seo_compliance.py  # SEO validation tests
└── docs/
    ├── README.md               # Module documentation
    ├── CHANGELOG.md            # Version history
    └── API_REFERENCE.md        # API documentation
```

### **Option 2: Modular Approach (Alternative)**

If you prefer separation of concerns:

```
modula_url_routing_core/        # Core URL routing infrastructure
├── models/
│   ├── ir_http.py
│   └── url_mapping.py
└── controllers/
    └── redirect_controller.py

modula_url_routing_shop/        # Shop-specific routing
├── controllers/
│   ├── category_routing.py
│   └── product_routing.py
└── models/
    ├── product_template.py
    └── product_public_category.py

modula_url_routing_blog/        # Blog-specific routing
├── controllers/
│   └── blog_routing.py
└── models/
    ├── blog_blog.py
    └── blog_post.py

modula_url_routing_custom/      # Custom page routing
└── controllers/
    └── custom_page_routing.py
```

## 📋 **Module Dependencies and Integration**

### **Dependency Hierarchy**
```
modula_url_routing
├── Depends on:
│   ├── website (core website functionality)
│   ├── website_sale (shop functionality)
│   ├── website_blog (blog functionality)
│   ├── modula_web (custom website extensions)
│   └── modula_product (custom product functionality)
├── Optional dependencies:
│   ├── website_helpdesk (if helpdesk routing needed)
│   └── portal (if portal routing needed)
└── Depended by:
    └── (future modules that need clean URLs)
```

### **Integration Points**

#### **A. With Existing Modula Modules**
```python
# Integration with modula_web
class CustomWebsiteSale(WebsiteSale):
    # Existing shop controller in modula_web
    # Will be extended by URL routing module
    pass

# Integration with modula_product  
class ProductTemplate(models.Model):
    _inherit = 'product.template'
    # Existing product customizations
    # Will be extended with URL generation
```

#### **B. With Core Odoo Modules**
```python
# website_sale integration
from odoo.addons.website_sale.controllers.main import WebsiteSale

# website_blog integration  
from odoo.addons.website_blog.controllers.main import WebsiteBlog

# http_routing integration
from odoo.addons.http_routing.models.ir_http import IrHttp
```

## 🔧 **Implementation Strategy**

### **Phase 1: Core Infrastructure**
1. Create `modula_url_routing` module
2. Implement URL mapping model
3. Create enhanced ir.http methods
4. Set up basic redirect controller

### **Phase 2: Shop URL Routing**
1. Implement category routing controller
2. Implement product routing controller
3. Override product/category URL generation
4. Create shop URL mappings

### **Phase 3: Blog URL Routing**
1. Implement blog routing controller
2. Override blog URL generation
3. Create blog URL mappings

### **Phase 4: Custom Page Routing**
1. Implement custom page routing
2. Create specific page mappings
3. Handle special cases

### **Phase 5: Testing and Optimization**
1. Comprehensive testing
2. Performance optimization
3. SEO validation
4. Documentation completion

## 📦 **Module Manifest Configuration**

### **Complete Manifest File**
```python
# modula_url_routing/__manifest__.py
{
    'name': 'Modula URL Routing',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Clean URL routing without IDs for SEO optimization',
    'description': '''
        Transform Odoo website URLs from ID-based slugs to clean, SEO-friendly URLs.
        
        Features:
        - Clean category URLs: /sofas/modular-sofas
        - Clean product URLs: /loft-2-seater  
        - Clean blog URLs: /blog/living-journal/grand-opening
        - Custom page URLs: /warranty-claim-form
        - Automatic 301 redirects from old URLs
        - SEO optimization and performance monitoring
    ''',
    'author': 'Carpet Call',
    'website': 'https://carpetcall.com.au/',
    'depends': [
        # Core dependencies
        'website',
        'website_sale',
        'website_blog',
        'http_routing',
        
        # Custom dependencies
        'modula_web',
        'modula_product',
    ],
    'data': [
        # Security
        'security/ir.model.access.csv',
        
        # Data
        'data/url_redirects.xml',
        
        # Views
        'views/url_mapping_views.xml',
        'views/website_templates.xml',
    ],
    'demo': [
        'demo/demo_url_mappings.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'modula_url_routing/static/src/js/url_tracking.js',
            'modula_url_routing/static/src/css/url_routing.css',
        ],
        'web.assets_backend': [
            'modula_url_routing/static/src/js/url_mapping_widget.js',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
    'post_init_hook': 'post_init_hook',
    'uninstall_hook': 'uninstall_hook',
}
```

### **Installation Hooks**
```python
# __init__.py
from . import controllers
from . import models

def post_init_hook(env):
    """Create initial URL mappings after module installation"""
    from .data.create_url_mappings import create_initial_url_mappings
    create_initial_url_mappings(env)

def uninstall_hook(env):
    """Clean up URL mappings when module is uninstalled"""
    env['url.mapping'].search([]).unlink()
    
    # Restore original URLs
    products = env['product.template'].search([])
    products._compute_website_url()
    
    categories = env['product.public.category'].search([])
    categories._compute_website_url()
```

## 🔒 **Security Configuration**

### **Access Rights**
```csv
# security/ir.model.access.csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_url_mapping_public,url.mapping.public,model_url_mapping,,1,0,0,0
access_url_mapping_user,url.mapping.user,model_url_mapping,base.group_user,1,1,0,0
access_url_mapping_manager,url.mapping.manager,model_url_mapping,website.group_website_designer,1,1,1,1
access_url_mapping_admin,url.mapping.admin,model_url_mapping,base.group_system,1,1,1,1
```

### **Record Rules (if needed)**
```xml
<!-- security/url_mapping_security.xml -->
<odoo noupdate="1">
    <record id="url_mapping_company_rule" model="ir.rule">
        <field name="name">URL Mapping: multi-company</field>
        <field name="model_id" ref="model_url_mapping"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
    </record>
</odoo>
```

## 📊 **Performance Considerations**

### **Database Optimization**
```sql
-- Add indexes for URL mapping lookups
CREATE INDEX idx_url_mapping_old_url ON url_mapping(old_url);
CREATE INDEX idx_url_mapping_new_url ON url_mapping(new_url);
CREATE INDEX idx_url_mapping_model_record ON url_mapping(model_name, record_id);
```

### **Caching Strategy**
```python
# models/url_mapping.py
from odoo.tools import ormcache

class UrlMapping(models.Model):
    _name = 'url.mapping'
    
    @ormcache('old_url')
    def _get_mapping_by_old_url(self, old_url):
        """Cached lookup for URL mappings"""
        return self.search([('old_url', '=', old_url), ('active', '=', True)], limit=1)
    
    def clear_cache(self):
        """Clear URL mapping cache"""
        self._get_mapping_by_old_url.clear_cache(self)
```

## 🚀 **Deployment Considerations**

### **Staging Environment Setup**
1. Install module in staging
2. Run URL migration scripts
3. Test all URL patterns
4. Validate SEO compliance
5. Performance testing

### **Production Deployment**
1. Database backup
2. Module installation
3. URL migration execution
4. Monitoring setup
5. Rollback plan ready

### **Monitoring and Maintenance**
1. URL health checks
2. Performance monitoring
3. SEO tracking
4. Error logging
5. Regular maintenance

---

**Module Structure Status**: Complete plan ready for implementation. Proceed with development following this structure.
