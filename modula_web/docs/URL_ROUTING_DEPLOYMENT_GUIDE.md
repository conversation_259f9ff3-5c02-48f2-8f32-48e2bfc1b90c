# URL Routing Deployment and Testing Guide

## 🚀 **Deployment Strategy**

### **Phase 1: Development Environment Setup**

#### **1. Module Installation**
```bash
# Navigate to Odoo directory
cd /home/<USER>/odoo/itms/modula18

# Install the new module
./odoo/odoo-bin -c .vscode/modula.conf -i modula_url_routing -d modula18_dev --test-enable --stop-after-init

# Verify installation
./odoo/odoo-bin -c .vscode/modula.conf -u modula_url_routing -d modula18_dev --test-enable --stop-after-init
```

#### **2. Initial Data Migration**
```python
# Execute in Odoo shell
from odoo import api, SUPERUSER_ID

def migrate_urls():
    with api.Environment.manage():
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Run URL mapping creation
        from odoo.addons.modula_url_routing.data.create_url_mappings import create_initial_url_mappings
        create_initial_url_mappings(env)
        
        env.cr.commit()
        print("URL mappings created successfully")

migrate_urls()
```

### **Phase 2: Testing Strategy**

#### **A. Unit Testing Framework**
```python
# tests/test_url_routing.py
from odoo.tests import TransactionCase, tagged

@tagged('url_routing')
class TestUrlRouting(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.product = self.env['product.template'].create({
            'name': 'Test Sofa Modular',
            'website_published': True,
        })
        self.category = self.env['product.public.category'].create({
            'name': 'Test Category',
        })
    
    def test_clean_slug_generation(self):
        """Test clean slug generation without IDs"""
        clean_slug = self.env['ir.http']._slugify_clean('Test Sofa Modular')
        self.assertEqual(clean_slug, 'test-sofa-modular')
    
    def test_product_clean_url_generation(self):
        """Test product clean URL generation"""
        self.product._compute_clean_url()
        self.assertEqual(self.product.clean_url, '/test-sofa-modular')
    
    def test_url_mapping_creation(self):
        """Test URL mapping creation"""
        mapping_count = self.env['url.mapping'].search_count([
            ('model_name', '=', 'product.template'),
            ('record_id', '=', self.product.id)
        ])
        self.assertEqual(mapping_count, 1)
    
    def test_category_url_structure(self):
        """Test category URL structure"""
        parent = self.env['product.public.category'].create({
            'name': 'Sofas',
        })
        child = self.env['product.public.category'].create({
            'name': 'Modular Sofas',
            'parent_id': parent.id,
        })
        child._compute_clean_url()
        self.assertEqual(child.clean_url, '/sofas/modular-sofas')
```

#### **B. Integration Testing**
```python
# tests/test_url_integration.py
from odoo.tests import HttpCase, tagged

@tagged('url_routing', 'post_install', '-at_install')
class TestUrlIntegration(HttpCase):
    
    def test_product_url_redirect(self):
        """Test product URL redirect functionality"""
        # Create test product
        product = self.env['product.template'].create({
            'name': 'Integration Test Sofa',
            'website_published': True,
        })
        
        # Test clean URL access
        response = self.url_open('/integration-test-sofa')
        self.assertEqual(response.status_code, 301)
        
        # Verify redirect target
        expected_url = f"/shop/{self.env['ir.http']._slug(product)}"
        self.assertIn(expected_url, response.headers.get('Location', ''))
    
    def test_category_url_redirect(self):
        """Test category URL redirect functionality"""
        parent = self.env['product.public.category'].create({
            'name': 'Test Sofas',
        })
        child = self.env['product.public.category'].create({
            'name': 'Test Modular',
            'parent_id': parent.id,
        })
        
        # Test clean URL access
        response = self.url_open('/test-sofas/test-modular')
        self.assertEqual(response.status_code, 301)
    
    def test_blog_url_redirect(self):
        """Test blog URL redirect functionality"""
        blog = self.env['blog.blog'].create({
            'name': 'Test Living Journal',
        })
        post = self.env['blog.post'].create({
            'name': 'Test Grand Opening',
            'blog_id': blog.id,
            'website_published': True,
        })
        
        # Test clean URL access
        response = self.url_open('/blog/test-living-journal/test-grand-opening')
        self.assertEqual(response.status_code, 301)
```

#### **C. Performance Testing**
```python
# tests/test_url_performance.py
import time
from odoo.tests import TransactionCase, tagged

@tagged('url_routing', 'performance')
class TestUrlPerformance(TransactionCase):
    
    def test_url_mapping_lookup_performance(self):
        """Test URL mapping lookup performance"""
        # Create multiple URL mappings
        mappings = []
        for i in range(1000):
            mappings.append({
                'old_url': f'/shop/test-product-{i}-{i}',
                'new_url': f'/test-product-{i}',
                'model_name': 'product.template',
                'record_id': i,
            })
        self.env['url.mapping'].create(mappings)
        
        # Test lookup performance
        start_time = time.time()
        for i in range(100):
            mapping = self.env['url.mapping'].find_by_old_url(f'/shop/test-product-{i}-{i}')
            self.assertTrue(mapping)
        end_time = time.time()
        
        # Should complete 100 lookups in under 1 second
        self.assertLess(end_time - start_time, 1.0)
    
    def test_clean_slug_generation_performance(self):
        """Test clean slug generation performance"""
        test_names = [f'Test Product Name {i}' for i in range(1000)]
        
        start_time = time.time()
        for name in test_names:
            slug = self.env['ir.http']._slugify_clean(name)
            self.assertTrue(slug)
        end_time = time.time()
        
        # Should generate 1000 slugs in under 0.5 seconds
        self.assertLess(end_time - start_time, 0.5)
```

### **Phase 3: SEO Validation**

#### **A. SEO Testing Checklist**
```python
# tests/test_seo_compliance.py
from odoo.tests import HttpCase, tagged
import re

@tagged('url_routing', 'seo')
class TestSeoCompliance(HttpCase):
    
    def test_url_structure_seo_compliance(self):
        """Test URL structure follows SEO best practices"""
        product = self.env['product.template'].create({
            'name': 'SEO Test Product',
            'website_published': True,
        })
        
        clean_url = product.clean_url
        
        # URL should not contain IDs
        self.assertNotRegex(clean_url, r'-\d+$')
        
        # URL should be lowercase
        self.assertEqual(clean_url, clean_url.lower())
        
        # URL should use hyphens, not underscores
        self.assertNotIn('_', clean_url)
        
        # URL should not have special characters
        self.assertRegex(clean_url, r'^/[a-z0-9-]+$')
    
    def test_canonical_url_generation(self):
        """Test canonical URL generation"""
        product = self.env['product.template'].create({
            'name': 'Canonical Test Product',
            'website_published': True,
        })
        
        # Test that clean URL is set as canonical
        self.assertEqual(product.website_url, product.clean_url)
    
    def test_redirect_status_codes(self):
        """Test proper redirect status codes"""
        product = self.env['product.template'].create({
            'name': 'Redirect Test Product',
            'website_published': True,
        })
        
        # Test 301 redirect from clean URL to standard URL
        response = self.url_open('/redirect-test-product')
        self.assertEqual(response.status_code, 301)
```

### **Phase 4: Production Deployment**

#### **A. Pre-Deployment Checklist**
- [ ] All unit tests passing
- [ ] Integration tests passing
- [ ] Performance tests meeting benchmarks
- [ ] SEO compliance validated
- [ ] Backup of production database created
- [ ] Rollback plan documented

#### **B. Deployment Steps**
```bash
# 1. Deploy module to production
git checkout production
git merge feature/url-routing
git push origin production

# 2. Install module in production
./odoo/odoo-bin -c production.conf -i modula_url_routing -d production_db --stop-after-init

# 3. Run URL migration
./odoo/odoo-bin shell -c production.conf -d production_db
>>> exec(open('migrate_urls.py').read())

# 4. Verify deployment
./odoo/odoo-bin -c production.conf -d production_db --test-enable --stop-after-init
```

#### **C. Post-Deployment Monitoring**
```python
# monitoring/url_health_check.py
import requests
import logging

def check_url_health():
    """Monitor URL routing health"""
    test_urls = [
        '/sofas/modular-sofas',
        '/loft-2-seater',
        '/blog/modula-living-journal',
        '/warranty-claim-form',
    ]
    
    for url in test_urls:
        try:
            response = requests.get(f'https://your-domain.com{url}')
            if response.status_code not in [200, 301, 302]:
                logging.error(f'URL {url} returned status {response.status_code}')
            else:
                logging.info(f'URL {url} is healthy')
        except Exception as e:
            logging.error(f'Error checking URL {url}: {str(e)}')

# Run health check every 5 minutes
if __name__ == '__main__':
    check_url_health()
```

### **Phase 5: Rollback Plan**

#### **A. Emergency Rollback Procedure**
```bash
# 1. Disable URL routing module
./odoo/odoo-bin -c production.conf -d production_db --stop-after-init -u base

# 2. Deactivate URL mappings
./odoo/odoo-bin shell -c production.conf -d production_db
>>> env['url.mapping'].search([]).write({'active': False})

# 3. Restore from backup if necessary
pg_restore -d production_db backup_before_url_routing.sql

# 4. Restart services
systemctl restart odoo
systemctl restart nginx
```

#### **B. Gradual Rollout Strategy**
1. **Week 1**: Deploy to staging environment
2. **Week 2**: Deploy to 10% of production traffic
3. **Week 3**: Deploy to 50% of production traffic
4. **Week 4**: Full production deployment

### **Phase 6: Success Metrics**

#### **A. Technical Metrics**
- [ ] All URLs follow new clean format
- [ ] 301 redirects working correctly (>99% success rate)
- [ ] No broken links detected
- [ ] Page load times maintained or improved
- [ ] Search engine indexing successful

#### **B. SEO Metrics**
- [ ] Improved URL readability scores
- [ ] Better search engine rankings
- [ ] Increased organic traffic
- [ ] Reduced bounce rates

#### **C. User Experience Metrics**
- [ ] Cleaner, more memorable URLs
- [ ] Improved navigation experience
- [ ] Faster page loads
- [ ] Positive user feedback

---

**Deployment Status**: Ready for implementation. Follow this guide for systematic deployment and monitoring.
