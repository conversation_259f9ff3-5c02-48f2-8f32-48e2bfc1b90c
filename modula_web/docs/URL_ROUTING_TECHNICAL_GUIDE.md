# URL Routing Technical Implementation Guide

## 🚀 **Advanced Controller Implementation**

### **Product Routing Controller**
```python
# controllers/product_routing.py
from odoo import http
from odoo.http import request
import werkzeug

class ProductRouting(http.Controller):
    
    @http.route([
        '/<string:product_name>',
    ], type='http', auth="public", website=True, sitemap=True)
    def product_clean_url(self, product_name, **kwargs):
        """Handle clean product URLs like /loft-2-seater"""
        
        # Find product by clean name
        product = self._find_product_by_clean_name(product_name)
        
        if not product:
            # Check URL mapping for redirects
            clean_url = f"/{product_name}"
            mapping = request.env['url.mapping'].find_by_new_url(clean_url)
            if mapping:
                return request.redirect(mapping.old_url, code=302)
            
            raise werkzeug.exceptions.NotFound()
        
        # Handle attribute values in fragment
        fragment = ''
        if 'attribute_values' in kwargs:
            # Convert attribute IDs to names for clean URLs
            attribute_values = kwargs.pop('attribute_values')
            clean_attributes = self._convert_attribute_values_to_names(
                product, attribute_values
            )
            if clean_attributes:
                fragment = f"#attribute_values={clean_attributes}"
        
        # Redirect to standard product URL
        product_url = f"/shop/{request.env['ir.http']._slug(product)}"
        
        # Preserve other query parameters
        if kwargs:
            query_string = werkzeug.urls.url_encode(kwargs)
            product_url += f"?{query_string}"
        
        product_url += fragment
        return request.redirect(product_url, code=301)
    
    def _find_product_by_clean_name(self, product_name):
        """Find product by clean name"""
        Product = request.env['product.template']
        
        # Convert URL name back to search pattern
        name_pattern = product_name.replace('-', ' ')
        
        # Search for products
        products = Product.search([
            ('website_published', '=', True),
            ('name', 'ilike', name_pattern)
        ])
        
        # Find exact match by slug
        for product in products:
            if request.env['ir.http']._slugify_clean(product.name) == product_name:
                return product
        
        return None
    
    def _convert_attribute_values_to_names(self, product, attribute_values):
        """Convert attribute value IDs to clean names"""
        if not attribute_values:
            return ''
        
        try:
            value_ids = [int(x) for x in attribute_values.split(',')]
            values = request.env['product.attribute.value'].browse(value_ids)
            
            clean_names = []
            for value in values:
                clean_name = request.env['ir.http']._slugify_clean(value.name)
                clean_names.append(clean_name)
            
            return ','.join(clean_names)
        except (ValueError, TypeError):
            return attribute_values
```

### **Blog Routing Controller**
```python
# controllers/blog_routing.py
from odoo import http
from odoo.http import request
import werkzeug

class BlogRouting(http.Controller):
    
    @http.route([
        '/blog/<string:blog_name>',
        '/blog/<string:blog_name>/page/<int:page>',
        '/blog/<string:blog_name>/<string:post_name>',
    ], type='http', auth="public", website=True, sitemap=True)
    def blog_clean_url(self, blog_name, post_name=None, page=1, **kwargs):
        """Handle clean blog URLs"""
        
        # Find blog by clean name
        blog = self._find_blog_by_clean_name(blog_name)
        if not blog:
            raise werkzeug.exceptions.NotFound()
        
        if post_name:
            # Handle blog post URL
            post = self._find_post_by_clean_name(blog, post_name)
            if not post:
                raise werkzeug.exceptions.NotFound()
            
            # Redirect to standard blog post URL
            post_url = f"/blog/{request.env['ir.http']._slug(blog)}/{request.env['ir.http']._slug(post)}"
            return request.redirect(post_url, code=301)
        else:
            # Handle blog listing URL
            blog_url = f"/blog/{request.env['ir.http']._slug(blog)}"
            if page > 1:
                blog_url += f"/page/{page}"
            
            if kwargs:
                query_string = werkzeug.urls.url_encode(kwargs)
                blog_url += f"?{query_string}"
            
            return request.redirect(blog_url, code=301)
    
    def _find_blog_by_clean_name(self, blog_name):
        """Find blog by clean name"""
        Blog = request.env['blog.blog']
        name_pattern = blog_name.replace('-', ' ')
        
        blogs = Blog.search([('name', 'ilike', name_pattern)])
        for blog in blogs:
            if request.env['ir.http']._slugify_clean(blog.name) == blog_name:
                return blog
        return None
    
    def _find_post_by_clean_name(self, blog, post_name):
        """Find blog post by clean name"""
        Post = request.env['blog.post']
        name_pattern = post_name.replace('-', ' ')
        
        posts = Post.search([
            ('blog_id', '=', blog.id),
            ('name', 'ilike', name_pattern),
            ('website_published', '=', True)
        ])
        
        for post in posts:
            if request.env['ir.http']._slugify_clean(post.name) == post_name:
                return post
        return None
```

### **Custom Page Routing Controller**
```python
# controllers/custom_page_routing.py
from odoo import http
from odoo.http import request

class CustomPageRouting(http.Controller):
    
    # Custom page mappings
    CUSTOM_PAGES = {
        'warranty-claim-form': '/helpdesk/warranty-claim-2',
        'contact-us': '/contactus',
        'our-story': '/our-story',
        'showroom': '/showroom',
    }
    
    @http.route([
        '/warranty-claim-form',
        '/contact-us',
    ], type='http', auth="public", website=True, sitemap=True)
    def custom_page_routing(self, **kwargs):
        """Handle custom page clean URLs"""
        
        # Get current path
        current_path = request.httprequest.path.strip('/')
        
        # Find target URL
        target_url = self.CUSTOM_PAGES.get(current_path)
        if not target_url:
            raise werkzeug.exceptions.NotFound()
        
        # Preserve query parameters
        if kwargs:
            query_string = werkzeug.urls.url_encode(kwargs)
            target_url += f"?{query_string}"
        
        return request.redirect(target_url, code=301)
```

## 🔄 **Model URL Generation Overrides**

### **Product Template URL Override**
```python
# models/product_template.py
from odoo import models, fields, api

class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    clean_url = fields.Char('Clean URL', compute='_compute_clean_url', store=True)
    
    @api.depends('name')
    def _compute_clean_url(self):
        """Compute clean URL without ID"""
        for product in self:
            if product.name:
                clean_name = self.env['ir.http']._slugify_clean(product.name)
                product.clean_url = f"/{clean_name}"
            else:
                product.clean_url = ''
    
    def _compute_website_url(self):
        """Override to use clean URLs"""
        super()._compute_website_url()
        for product in self:
            if product.clean_url:
                product.website_url = product.clean_url
    
    @api.model_create_multi
    def create(self, vals_list):
        """Create URL mapping when product is created"""
        products = super().create(vals_list)
        for product in products:
            if product.website_published:
                self._create_url_mapping(product)
        return products
    
    def write(self, vals):
        """Update URL mapping when product is updated"""
        result = super().write(vals)
        if 'name' in vals or 'website_published' in vals:
            for product in self:
                if product.website_published:
                    self._update_url_mapping(product)
        return result
    
    def _create_url_mapping(self, product):
        """Create URL mapping for product"""
        old_url = f"/shop/{self.env['ir.http']._slug(product)}"
        new_url = product.clean_url
        
        if old_url != new_url:
            self.env['url.mapping'].create({
                'old_url': old_url,
                'new_url': new_url,
                'model_name': 'product.template',
                'record_id': product.id,
            })
    
    def _update_url_mapping(self, product):
        """Update existing URL mapping"""
        mapping = self.env['url.mapping'].search([
            ('model_name', '=', 'product.template'),
            ('record_id', '=', product.id)
        ], limit=1)
        
        if mapping:
            mapping.new_url = product.clean_url
        else:
            self._create_url_mapping(product)
```

### **Category URL Override**
```python
# models/product_public_category.py
from odoo import models, fields, api

class ProductPublicCategory(models.Model):
    _inherit = 'product.public.category'
    
    clean_url = fields.Char('Clean URL', compute='_compute_clean_url', store=True)
    
    @api.depends('name', 'parent_id.name')
    def _compute_clean_url(self):
        """Compute clean category URL"""
        for category in self:
            if category.parent_id:
                parent_slug = self.env['ir.http']._slugify_clean(category.parent_id.name)
                child_slug = self.env['ir.http']._slugify_clean(category.name)
                category.clean_url = f"/{parent_slug}/{child_slug}"
            else:
                category.clean_url = f"/{self.env['ir.http']._slugify_clean(category.name)}"
    
    def _compute_website_url(self):
        """Override to use clean URLs"""
        super()._compute_website_url()
        for category in self:
            if category.clean_url:
                category.website_url = category.clean_url
```

## 🔧 **Migration and Data Management**

### **URL Mapping Creation Script**
```python
# data/create_url_mappings.py
def create_initial_url_mappings(env):
    """Create URL mappings for existing records"""
    
    # Create product mappings
    products = env['product.template'].search([('website_published', '=', True)])
    for product in products:
        old_url = f"/shop/{env['ir.http']._slug(product)}"
        clean_name = env['ir.http']._slugify_clean(product.name)
        new_url = f"/{clean_name}"
        
        if old_url != new_url:
            env['url.mapping'].create({
                'old_url': old_url,
                'new_url': new_url,
                'model_name': 'product.template',
                'record_id': product.id,
            })
    
    # Create category mappings
    categories = env['product.public.category'].search([])
    for category in categories:
        old_url = f"/shop/category/{env['ir.http']._slug(category)}"
        
        if category.parent_id:
            parent_slug = env['ir.http']._slugify_clean(category.parent_id.name)
            child_slug = env['ir.http']._slugify_clean(category.name)
            new_url = f"/{parent_slug}/{child_slug}"
        else:
            new_url = f"/{env['ir.http']._slugify_clean(category.name)}"
        
        if old_url != new_url:
            env['url.mapping'].create({
                'old_url': old_url,
                'new_url': new_url,
                'model_name': 'product.public.category',
                'record_id': category.id,
            })
    
    # Create blog mappings
    blogs = env['blog.blog'].search([])
    for blog in blogs:
        old_url = f"/blog/{env['ir.http']._slug(blog)}"
        clean_name = env['ir.http']._slugify_clean(blog.name)
        new_url = f"/blog/{clean_name}"
        
        if old_url != new_url:
            env['url.mapping'].create({
                'old_url': old_url,
                'new_url': new_url,
                'model_name': 'blog.blog',
                'record_id': blog.id,
            })
    
    # Create blog post mappings
    posts = env['blog.post'].search([('website_published', '=', True)])
    for post in posts:
        old_url = f"/blog/{env['ir.http']._slug(post.blog_id)}/{env['ir.http']._slug(post)}"
        blog_slug = env['ir.http']._slugify_clean(post.blog_id.name)
        post_slug = env['ir.http']._slugify_clean(post.name)
        new_url = f"/blog/{blog_slug}/{post_slug}"
        
        if old_url != new_url:
            env['url.mapping'].create({
                'old_url': old_url,
                'new_url': new_url,
                'model_name': 'blog.post',
                'record_id': post.id,
            })
```

---

**Implementation Status**: Technical components ready for development. Proceed with module creation and testing.
