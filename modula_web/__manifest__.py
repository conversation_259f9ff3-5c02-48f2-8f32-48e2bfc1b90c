{
    'name': 'Modula Web',
    'version': '********.0',
    'category': 'website',
    'description': 'Extended Website',
    'author': 'Carpet Call',
    'website': 'https://carpetcall.com.au/',
    'depends': ['website', 'web', 'auth_signup', 'website_sale', 'modula_product', 'website_blog', 'account',
                'website_sale_autocomplete', 'website_helpdesk', 'portal'],
    'data': [
        # View Files
        'views/website_extra.xml',
        'views/home.xml',
        'views/footer.xml',
        'views/header.xml',
        'views/svg-icons.xml',

        # Contact pages
        'views/contacts/website_contact_us.xml',
        'views/contacts/contact_us_thanks.xml',

        # Helpdesk
        'views/helpdesk/warranty-claim.xml',
        'views/helpdesk/warranty-claim-thanks.xml',

        # Product pages
        'views/products/products_list.xml',
        'views/products/product_detail.xml',
        'views/products/variant_templates.xml',

        # Blog pages
        'views/blogs/blog_list.xml',

        # Checkout
        'views/checkout/shopping_cart.xml',

        # Payments
        'views/payment/payment.xml',

        # Login / Signup
        'views/users/login.xml',
        'views/users/signup.xml',

        # portal user profile
        'views/users/edit_user_account_info.xml',
        'views/users/my_account.xml',
        'views/users/portal_security.xml',

        # Miscellaneous Pages
        'views/pages/miscellaneous_pages.xml',
        'views/pages/showroom.xml',
        'views/pages/our_story.xml',
        'views/pages/shipping_and_delivery.xml',
        'views/pages/returns_and_refunds.xml',
        'views/pages/privacy_policy.xml',
        'views/pages/terms_and_conditions.xml',
        'views/pages/care_and_maintenance.xml',

    ],
    'assets': {
        'web.assets_frontend': [
            'modula_web/static/src/scss/variables.scss',
            'modula_web/static/src/scss/style.scss',
            'modula_web/static/src/scss/footer.scss',
            'modula_web/static/src/scss/products.scss',
            'modula_web/static/src/scss/blog.scss',
            'modula_web/static/src/scss/cart.scss',
            # 'modula_web/static/src/scss/preloader.scss',

            'modula_web/static/src/js/header.js',
            # 'modula_web/static/src/js/preloader.js',
            # 'modula_web/static/src/js/helpdesk_address.js',
            'modula_web/static/src/js/address_form.js',
            'modula_web/static/src/js/helpdesk_address_validation_form.js',
            'modula_web/static/src/js/warranty_form_result_patch.js',
            'modula_web/static/src/js/product_detail_page_patch.js',
            'modula_web/static/src/js/update_tax_amount.js',
            # 'modula_web/static/src/js/navigation.js',

            # Landing page specific assets
            'modula_web/static/src/js/landing/landing_page.js',
            'modula_web/static/src/js/landing/landing_page.xml',
        ],
    },
    "installable": True,
    "application": True,
    "license": "LGPL-3",
}
