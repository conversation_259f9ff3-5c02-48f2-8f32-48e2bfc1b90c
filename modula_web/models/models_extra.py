from odoo import models, _lt, fields, api, _
from odoo.exceptions import UserError
import logging


class Website(models.Model):
    _inherit = 'website'

    def _get_checkout_step_list(self):
        steps = super()._get_checkout_step_list()
        redirect_to_sign_in = self.account_on_checkout == 'mandatory' and self.is_public_user()
        for step in steps:
            if 'website_sale.cart' in step[0]:
                step[1]['name'] = _lt("Review Cart")
                step[1]['main_button'] = _lt("Checkout") if redirect_to_sign_in else _lt("Checkout")
                step[1][
                    'main_button_href'] = f'{"/web/login?redirect=" if redirect_to_sign_in else ""}/shop/checkout?try_skip_step=true'
        return steps

    @staticmethod
    def _get_product_sort_mapping():
        return [
            ('website_sequence asc', _("Featured")),
            ('create_date desc', _("Newest Arrivals")),
            ('name asc', _("Name (A-Z)")),
            ('min_variant_price asc', _("Price - Low to High")),
            ('min_variant_price desc', _("Price - High to Low")),
        ]

    shop_default_sort = fields.Selection(selection='_get_product_sort_mapping', default='website_sequence asc',
                                         required=True)


class ProductProduct(models.Model):
    _inherit = 'product.product'

    @api.model
    def write(self, vals):
        res = super(ProductProduct, self).write(vals)
        if 'fix_price' in vals:
            # Find all templates related to the updated variants
            templates = self.mapped('product_tmpl_id')
            # Trigger recomputation of the lowest_variant_price field
            templates._compute_lowest_variant_price()
        return res


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    min_variant_price = fields.Float(string='Minimum Variant Price', compute='_compute_lowest_variant_price',
                                     store=True)

    @api.depends('product_variant_ids.list_price')
    def _compute_lowest_variant_price(self):
        for template in self:
            prices_with_tax = []
            for variant in template.product_variant_ids:
                # Retrieve the taxes applicable to the variant
                # taxes = variant.taxes_id
                taxes = variant.sudo().taxes_id._filter_taxes_by_company(self.env.company)
                # Compute the price including taxes
                price_with_tax = taxes.compute_all(
                    variant.list_price,
                    currency=variant.currency_id,
                    quantity=1.0,
                    product=variant,
                    partner=None
                )['total_included']
                # Consider only positive prices
                if price_with_tax > 0.0:
                    prices_with_tax.append(price_with_tax)
            # Determine the lowest price including taxes
            template.min_variant_price = min(prices_with_tax) if prices_with_tax else 0.0

    def get_cheapest_variant_price(self, category, partner=None):
        """
        Returns a tuple containing:
        - the minimum non-zero fixed sale price including applicable taxes,
        - the corresponding product variant (combination),
        - the ID of the 'Cover' attribute value,
        - the ID of the 'Violino Colour Range' attribute value.

        For 'Leather Sofas' category, only considers variants with the 'Cover' attribute set to 'Leather'.
        """
        self.ensure_one()
        is_leather_category = category.name == 'Leather Sofas'
        variant_prices = []

        for variant in self.product_variant_ids:
            # print(variant.default_code)
            base_price = variant.list_price
            # print(base_price)
            if base_price <= 0:
                continue

            if is_leather_category:
                cover_attributes = [
                    ptav.name
                    for ptav in variant.product_template_attribute_value_ids
                    if ptav.attribute_id.name == 'Cover'
                ]
                if 'Leather' not in cover_attributes:
                    continue

            taxes = variant.sudo().taxes_id.compute_all(
                base_price,
                currency=variant.currency_id,
                quantity=1.0,
                product=variant,
                partner=partner
            )
            total_price = taxes.get('total_included', 0.0)
            if total_price <= 0:
                continue

            # Extract the second part of combination_indices
            combination_parts = variant.combination_indices.split(',')
            second_part = int(combination_parts[1]) if len(combination_parts) > 1 else 0

            variant_prices.append((total_price, second_part, variant))

        if not variant_prices:
            # return 0.0, None, None, None
            return {
                'price': 0.0,
                'variant': None,
                'cover_attribute': None,
                'colour_attribute': None
            }

        # Sort by total_price and then by the second part of combination_indices
        variant_prices.sort(key=lambda x: (x[0], x[1]))
        cheapest_price, _, cheapest_variant = variant_prices[0]

        cover_attribute_id = next(
            (ptav.product_attribute_value_id.id
             for ptav in cheapest_variant.product_template_attribute_value_ids
             if ptav.attribute_id.name == 'Cover'),
            None
        )
        colour_attribute_id = next(
            (ptav.product_attribute_value_id.id
             for ptav in cheapest_variant.product_template_attribute_value_ids
             if ptav.attribute_id.name == 'Violino Colour Range'),
            None
        )

        return {
            'price': cheapest_price,
            'variant': cheapest_variant,
            'cover_attribute': cover_attribute_id,
            'colour_attribute': colour_attribute_id
        }

    def _get_sales_prices(self, website):
        # Return an empty dictionary if 'self' is empty
        if not self:
            return {}

        # Retrieve the pricelist, currency, and fiscal position associated with the website
        pricelist = website.pricelist_id
        currency = website.currency_id
        fiscal_position = website.fiscal_position_id.sudo()
        date = fields.Date.context_today(self)

        # Check if the current user has the group that enables price comparison on the website
        comparison_prices_enabled = self.env.user.has_group('website_sale.group_product_price_comparison')

        res = {}
        for template in self:
            # Determine the products to consider: variants if they exist, else the template itself
            products = template.product_variant_ids if template.product_variant_ids else template

            # Compute prices for the products using the pricelist
            pricelist_prices = pricelist._compute_price_rule(products, 1.0)

            # Initialize lists to track all prices and non-zero prices
            all_prices = []
            non_zero_prices = []

            # Iterate through each product to collect pricing information
            for product in products:
                # Retrieve the price and rule ID from the pricelist; fallback to product's list price if not found
                price, rule_id = pricelist_prices.get(product.id, (product.list_price, None))
                price = price or 0.0  # Ensure price is not None
                all_prices.append((price, product, rule_id))
                if price > 0.0:
                    non_zero_prices.append((price, product, rule_id))

            # Use non-zero prices if available; fallback to all_prices if only zero-priced variants exist
            candidates = non_zero_prices if non_zero_prices else all_prices
            # Select the candidate with the minimum price
            min_price, min_product, min_rule_id = sorted(candidates, key=lambda x: x[0])[0]

            # Retrieve taxes applicable to the product template and map them using the fiscal position
            product_taxes = template.sudo().taxes_id._filter_taxes_by_company(self.env.company)
            taxes = fiscal_position.map_tax(product_taxes)

            # Compute the reduced price with taxes applied
            template_price_vals = {
                'price_reduce': self._apply_taxes_to_price(
                    min_price, currency, product_taxes, taxes, template, website=website,
                ),
            }

            base_price = None
            # Retrieve the pricelist item corresponding to the applied rule
            pricelist_item = self.env['product.pricelist.item'].browse(min_rule_id)
            # Check if the pricelist item is configured to show discounts on the shop
            if pricelist_item._show_discount_on_shop():
                # Compute the base price before discount
                pricelist_base_price = pricelist_item._compute_price_before_discount(
                    product=min_product,
                    quantity=1.0,
                    date=date,
                    uom=min_product.uom_id,
                    currency=currency,
                )
                # Compare the base price with the minimum price to determine if a discount is applied
                if currency.compare_amounts(pricelist_base_price, min_price) == 1:
                    base_price = pricelist_base_price
                    # Apply taxes to the base price and add it to the result
                    template_price_vals['base_price'] = self._apply_taxes_to_price(
                        base_price, currency, product_taxes, taxes, template, website=website,
                    )

            # Fallback to compare_list_price if no base price is determined and comparison is enabled
            if not base_price and comparison_prices_enabled and template.compare_list_price:
                template_price_vals['base_price'] = template.currency_id._convert(
                    template.compare_list_price,
                    currency,
                    self.env.company,
                    date,
                    round=False,
                )

            # Assign the computed price values to the result dictionary
            res[template.id] = template_price_vals

        return res
