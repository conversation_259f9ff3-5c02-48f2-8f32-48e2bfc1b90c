<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <!--    Checkout Layout-->
    <template id="custom_checkout" inherit_id="website_sale.checkout" name="Custom checkout layout">
        <xpath expr="//t[@t-call='website_sale.checkout_layout']" position="replace">
            <t t-call="website_sale.checkout_layout">
                <t t-set="additional_title">Shop - Checkout</t>
                <t t-set="redirect" t-valuef="/shop/checkout"/>
                <!--Footer is enabled-->
                <t t-set="show_footer" t-value="True"/>
                <t t-set="same_shipping"
                   t-value="bool(order.partner_shipping_id==order.partner_invoice_id or only_services)"/>
                <div id="shop_checkout">
                    <!-- When delivery address is enabled. -->
                    <t t-if="order._has_deliverable_products()"
                       groups="account.group_delivery_invoice_address">
                        <t t-call="website_sale.delivery_address_row">
                            <t t-set="addresses" t-value="delivery_addresses"/>
                        </t>
                        <t t-call="website_sale.delivery_form">
                            <t t-set="selected_dm_id" t-value="order.carrier_id.id"/>
                        </t>
                    </t>
                    <t t-call="website_sale.billing_address_row">
                        <t t-set="addresses" t-value="billing_addresses"/>
                    </t>
                    <!-- When delivery address is disabled. -->
                    <t groups="!account.group_delivery_invoice_address"
                       t-call="website_sale.delivery_form">
                        <t t-set="selected_dm_id" t-value="order.carrier_id.id"/>
                    </t>
                </div>
            </t>

        </xpath>
    </template>

    <!--Show/Hide shopping cart breadcrumb-->
    <template id="hide_breadcrumb" inherit_id="website.step_wizard" name="Hide Shopping Cart Breadcrumb">
        <xpath expr="//div" position="replace">
            <t t-if="request.httprequest.path != '/shop/cart'">
                <div class="o_wizard d-flex flex-wrap justify-content-between justify-content-md-start my-3 my-sm-4">
                    <div class="d-flex flex-column flex-md-row align-items-end align-items-md-start justify-content-center">
                        <!-- Large screen views -->

                        <t t-foreach="wizard_step" t-as="step">
                            <t t-set="is_current_step" t-value="xmlid in step[0]"/>
                            <t t-if="is_current_step" t-set="current_step" t-value="step"/>
                            <span t-if="current_step"
                                  t-attf-class="#{not is_current_step and 'o_disabled'} d-none d-md-flex no-decoration">
                                <div t-attf-class="d-flex align-items-center {{'o_wizard_step_active fw-bold' if is_current_step else 'text-muted'}}">
                                    <p class="o_wizard_steplabel text-center mb-0">
                                        <t t-out="step[1]['name']"/>
                                    </p>
                                    <span t-if="not step_last"
                                          class="fa fa-angle-right d-inline-block align-middle mx-sm-3 text-muted fs-5"/>
                                </div>
                            </span>
                            <a t-else=""
                               class="d-none d-md-flex no-decoration"
                               t-att-href="step[1]['current_href']"
                               t-att-title="step[1]['name']">
                                <div class="d-flex align-items-center o_wizard_step-done">
                                    <p class="o_wizard_steplabel text-center mb-0">
                                        <t t-out="step[1]['name']"/>
                                    </p>
                                    <span t-if="not step_last"
                                          class="fa fa-angle-right d-inline-block align-middle mx-sm-3 text-muted fs-5"/>
                                </div>
                            </a>
                        </t>

                        <!-- Mobile screen views -->
                        <div class="d-flex d-md-none flex-column align-items-start">
                            <div class="dropdown">
                                <a class="dropdown-toggle fw-bold"
                                   role="button"
                                   data-bs-toggle="dropdown"
                                   aria-expanded="false"
                                   title="Steps">
                                    <t t-out="list(filter(lambda step: xmlid in step[0],wizard_step))[0][1]['name']"/>
                                </a>
                                <ul class="dropdown-menu">
                                    <t t-set="current_step" t-value="None"/>
                                    <li t-foreach="wizard_step" t-as="step">
                                        <t t-set="is_current_step" t-value="xmlid in step[0]"/>
                                        <t t-if="is_current_step">
                                            <t t-set="current_step" t-value="step"/>
                                            <t t-set="_steps_in_deg"
                                               t-value="(step_index + 1) / len(wizard_step) * 360"/>
                                            <t t-set="next_step"
                                               t-value="wizard_step[step_index+1] if step_index+1 &lt; len(wizard_step) else False"/>
                                        </t>
                                        <a t-if="not current_step"
                                           class="dropdown-item"
                                           t-att-href="step[1]['current_href']"
                                           t-out="step[1]['name']"
                                           t-att-title="step[1]['name']"/>
                                        <span t-else=""
                                              t-attf-class="dropdown-item {{'fw-bold' if is_current_step else 'text-muted o_disabled'}}"
                                              t-out="step[1]['name']"
                                              t-att-title="step[1]['name']"/>
                                    </li>
                                </ul>
                            </div>
                            <span t-if="next_step"
                                  class="d-inline-block d-md-none text-muted">
                                Next:
                                <t t-out="next_step[1]['name']"/>
                            </span>
                        </div>
                    </div>
                    <div class="o_wizard_circle_progress progress d-md-none position-relative rounded-circle ms-3 bg-transparent"
                         t-attf-style="--rightProgress:{{'180' if _steps_in_deg >= 180 else _steps_in_deg}}deg; --leftProgress:{{_steps_in_deg-180 if _steps_in_deg >= 180 else 0}}deg;">
                        <span class="o_wizard_circle_progress_left position-absolute start-0 top-0 z-1 overflow-hidden w-50 h-100 ">
                            <span class="progress-bar position-absolute start-100 top-0 w-100 h-100 border border-5 border-start-0 border-primary bg-transparent"/>
                        </span>
                        <span class="o_wizard_circle_progress_right position-absolute top-0 end-0 z-1 overflow-hidden w-50 h-100">
                            <span class="progress-bar position-absolute top-0 end-100 w-100 h-100 border border-5 border-end-0 border-primary bg-transparent"/>
                        </span>
                        <p class="mx-auto fw-bold">
                            <t t-out="wizard_step.index(current_step)+1"/>
                            of
                            <t t-out="len(wizard_step)"/>
                        </p>
                    </div>
                </div>
            </t>

            <t t-else="">
                <span style="display: inline-block; margin-top: 1rem;"></span>
            </t>
        </xpath>
    </template>

    <!--Rename Order Overview to Cart-->
    <template id="custom_cart" inherit_id="website_sale.cart" name="Custom Shopping Cart">
        <xpath expr="//t[@t-call='website_sale.checkout_layout']" position="replace">
            <t t-call="website_sale.checkout_layout">
                <t t-set="show_shorter_cart_summary" t-value="True"/>
                <t t-set="show_footer" t-value="True"/>
                <t t-set="oe_structure">
                    <!-- This is the drag-and-drop area for website building blocs at the end of each
                         checkout page. This is append at the of the page in `checkout_layout`. The
                         templates created in the database to store blocs are hooked using XPath on the
                         `oe_struture` element ID. Therefore, we can't use dynamic IDs (like with
                         t-att-id) and each template needs to define a div element. -->
                    <div class="oe_structure" id="oe_structure_website_sale_cart_2"/>
                </t>

                <div class="col">
                    <h3 class="mb-4">Cart</h3>
                    <div t-if="abandoned_proceed or access_token" class="alert alert-info mt8 mb8"
                         role="alert"> <!-- abandoned cart choices -->
                        <t t-if="abandoned_proceed">
                            <p>Your previous cart has already been completed.</p>
                            <p t-if="website_sale_order">Please proceed your current cart.</p>
                        </t>
                        <t t-if="access_token">
                            <p>This is your current cart.</p>
                            <p>
                                <strong>
                                    <a t-attf-href="/shop/cart/?access_token=#{access_token}&amp;revive=squash">Click
                                        here
                                    </a>
                                </strong>
                                if you want to restore your previous cart. Your current cart will be replaced with your
                                previous cart.
                            </p>
                            <p>
                                <strong>
                                    <a t-attf-href="/shop/cart/?access_token=#{access_token}&amp;revive=merge">Click
                                        here
                                    </a>
                                </strong>
                                if you want to merge your previous cart into current cart.
                            </p>
                        </t>
                    </div>
                    <t t-call="website_sale.cart_lines"/>
                    <div class="clearfix"/>
                    <div class="oe_structure" id="oe_structure_website_sale_cart_1"/>
                </div>
            </t>

        </xpath>
    </template>

    <!--Custom Cart Lines-->
    <template id="custom_cart_lines" inherit_id="website_sale.cart_lines">
        <xpath expr="//div[@id='cart_products']" position="replace">
            <div id="cart_products"
                 t-if="website_sale_order and website_sale_order.website_order_line"
                 class="js_cart_lines d-flex flex-column mb32">
                <t t-set="show_qty" t-value="is_view_active('website_sale.product_quantity')"/>
                <div t-foreach="website_sale_order.website_order_line"
                     t-as="line"
                     t-attf-class="o_cart_product d-flex align-items-stretch gap-3 #{line.linked_line_id and 'optional_product info'} #{not line_last and 'border-bottom pb-4'} #{line_index &gt; 0 and 'pt-4'}"
                     t-attf-data-product-id="#{line.product_id and line.product_id.id}">
                    <t t-if="line.product_id">
                        <div style="width: 64px">
                            <!--
                                Unsellable lines can have unpublished products, but portal users have no
                                access to unpublished product images. To ensure product images are
                                always shown for unsellable lines, we use the raw image data as src
                                (which doesn't require access, unlike the image URL).
                            -->
                            <img t-if="line._is_not_sellable_line() and line.product_id.image_128"
                                 t-att-src="image_data_uri(line.product_id.image_128)"
                                 class="o_image_64_max img rounded"
                                 t-att-alt="line.name_short"/>
                            <div t-elif="line.product_id.image_128"
                                 t-field="line.product_id.image_128"
                                 t-options="{
                                'widget': 'image',
                                'qweb_img_responsive': False,
                                'class': 'o_image_64_max rounded',
                            }"/>
                        </div>
                        <div class="flex-grow-1">
                            <t t-call="website_sale.cart_line_product_link">
                                <h6 t-field="line.product_id.product_tmpl_id.name"
                                    class="d-inline h6 fw-bold f-20"/>

                            </t>
                            <!--Variants of product-->
                            <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attr_value">
                                <p class="mb-0">
                                    <span class="fw-600">
                                        <t t-esc="attr_value.attribute_id.website_display_name"/>:
                                    </span>
                                    <t t-esc="attr_value.name.title()"/>
                                </p>
                            </t>
                            <t t-call="website_sale.cart_line_description_following_lines">
                                <t t-set="div_class" t-value="''"/>
                            </t>


                            <div name="o_wsale_cart_line_button_container">
                                <a href='#'
                                   class="mt-1 js_delete_product d-none d-md-inline-block small"
                                   aria-label="Remove from cart"
                                   title="Remove from cart">Remove
                                </a>
                                <button class="js_delete_product btn btn-light d-inline-block d-md-none"
                                        title="remove">
                                    <i class="fa fa-trash-o"/>
                                </button>
                            </div>
                            <t t-if="line.product_type == 'combo'">
                                <div t-foreach="line.linked_line_ids"
                                     t-as="combo_item_line"
                                     t-attf-class="{{'' if combo_item_line_last else 'pb-2'}}">
                                    <t t-call="website_sale.cart_combo_item_line"/>
                                </div>
                            </t>
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <div t-attf-class="css_quantity input-group mb-2"
                                 name="website_sale_cart_line_quantity">
                                <t t-if="not line._is_not_sellable_line()">
                                    <t t-if="show_qty">
                                        <a href="#"
                                           class="js_add_cart_json btn btn-link d-inline-block border-end-0"
                                           aria-label="Remove one"
                                           title="Remove one">
                                            <i class="position-relative z-1 fa fa-minus"/>
                                        </a>
                                        <input type="text"
                                               class="js_quantity quantity form-control border-start-0 border-end-0"
                                               t-att-data-line-id="line.id"
                                               t-att-data-product-id="line.product_id.id"
                                               t-att-value="line._get_displayed_quantity()"/>
                                        <t t-if="line._get_shop_warning(clear=False)">
                                            <a href="#" class="btn btn-link">
                                                <i class='fa fa-warning text-warning'
                                                   t-att-title="line._get_shop_warning()"
                                                   role="img"
                                                   aria-label="Warning"/>
                                            </a>
                                        </t>
                                        <a t-else=""
                                           href="#"
                                           class="js_add_cart_json d-inline-block float_left btn btn-link border-start-0"
                                           aria-label="Add one"
                                           title="Add one">
                                            <i class="fa fa-plus position-relative z-1"/>
                                        </a>
                                    </t>
                                    <t t-else="">
                                        <input type="hidden"
                                               class="js_quantity form-control quantity"
                                               t-att-data-line-id="line.id"
                                               t-att-data-product-id="line.product_id.id"
                                               t-att-value="line._get_displayed_quantity()"/>
                                    </t>
                                </t>
                                <t t-else="">
                                    <span class="w-100 text-muted" t-esc="int(line.product_uom_qty)"/>
                                    <input type="hidden"
                                           class="js_quantity quantity form-control"
                                           t-att-data-line-id="line.id"
                                           t-att-data-product-id="line.product_id.id"
                                           t-att-value="line._get_displayed_quantity()"/>
                                </t>
                            </div>
                            <div class="mb-0 h6 fw-bold text-end product_price" name="website_sale_cart_line_price">
                                <t t-if="line.discount">
                                    <del t-attf-class="#{'text-muted mr8'}"
                                         style="white-space: nowrap;"
                                         t-out="line._get_displayed_unit_price() * line.product_uom_qty"
                                         t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                </t>
                                <t t-set="product_price" t-value="line._get_cart_display_price()"/>
                                <t t-call="website_sale.cart_product_price"/>
                                <small t-if="not line._is_not_sellable_line() and line.product_id.base_unit_price"
                                       class="cart_product_base_unit_price d-block text-muted"
                                       groups="website_sale.group_show_uom_price">
                                    <t t-call='website_sale.base_unit_price'>
                                        <t t-set='product' t-value='line.product_id'/>
                                        <t t-set='combination_info'
                                           t-value="{'base_unit_price': product._get_base_unit_price(product_price/line.product_uom_qty)}"/>
                                    </t>
                                </small>
                            </div>
                        </div>
                    </t>
                </div>
            </div>
        </xpath>
    </template>

    <!-- Custom Checkout Summary in Cart page-->
    <template id="custom_total" inherit_id="website_sale.total" name="Cart Total Summary Extended">

        <xpath expr="//div[@id='cart_total']" position="replace">
            <div id="cart_total" t-if="website_sale_order and website_sale_order.website_order_line"
                 t-att-class="_cart_total_classes">
                <table class="table mb-0">

                    <!--Subtotal-->
                    <tr id="order_total_untaxed" class="d-none">
                        <td id="cart_total_subtotal"
                            class="border-0 pb-2 ps-0 pt-0 text-start text-muted"
                            colspan="2">
                            Subtotal
                        </td>
                        <td class="text-end border-0 pb-2 pe-0 pt-0">
                            <span t-field="website_sale_order.amount_untaxed"
                                  class="monetary_field"
                                  style="white-space: nowrap;"
                                  t-options="{'widget': 'monetary', 'display_currency': website_sale_order.currency_id}"/>
                        </td>
                    </tr>

                    <!-- Delivery-->
                    <tr t-if="website_sale_order._has_deliverable_products()"
                        id="order_delivery">
                        <td class="ps-0 pt-0 pb-2 border-0 text-muted" colspan="2">
                            Delivery
                        </td>
                        <td class="text-end pe-0 pt-0 pb-2 border-0">

                            <!--If no delivery carrier is selected-->
                            <span id="message_no_dm_set"
                                  t-att-class="'d-none' if website_sale_order.carrier_id else ''"
                                  title="Price will be updated after choosing a delivery method">
                                Calculated at checkout
                            </span>

                            <!-- If a delivery carrier is selected-->
                            <span t-out="website_sale_order.amount_delivery"
                                  t-att-class="'monetary_field' + ('' if website_sale_order.carrier_id else ' d-none')"
                                  style="white-space: nowrap;"
                                  t-options="{'widget': 'monetary', 'display_currency': website_sale_order.currency_id}"/>
                        </td>
                    </tr>

                    <!--Taxes- Hidden from DOM but used in js-->
                    <tr id="order_total_taxes" class="d-hide">
                        <td colspan="2" class="text-muted border-0 ps-0 pt-0 pb-3">GST</td>
                        <td class="text-end border-0 pe-0 pt-0 pb-3">

                            <span id="cart_tax_amount" t-field="website_sale_order.amount_tax"
                                  class="monetary_field"
                                  style="white-space: nowrap;"
                                  t-options="{'widget': 'monetary', 'display_currency': website_sale_order.currency_id}"/>
                            <t t-set="tax_amount" t-value="website_sale_order.amount_tax"/>

                        </td>
                    </tr>

                    <!--Promo Code-->
                    <tr t-if="not hide_promotions">
                        <td colspan="3" class="text-end text-xl-end border-0 p-0">
                            <span>
                                <t t-set="force_coupon" t-value="website_sale_order.pricelist_id.code"/>
                                <div t-if="not force_coupon" class="coupon_form mb-3">
                                    <t t-call="website_sale.coupon_form"/>
                                </div>
                            </span>
                        </td>
                    </tr>


                    <tr id="order_total" class="border-top">
                        <td colspan="2" class="border-0 ps-0 pt-3">
                            <strong>Total</strong>
                            <br/>
                            <small class="text-muted">Inc.

                                <!--                                <span>-->
                                <!--                                    <span t-field="website_sale_order.amount_tax" class="oe_price"/>-->
                                <!--                                </span>-->

                                <t t-call="modula_web.taxes"/>
                                in GST
                            </small>

                        </td>
                        <td class="text-end border-0 px-0 pt-3">
                            <strong t-field="website_sale_order.amount_total"
                                    class="monetary_field text-end p-0"
                                    t-options="{'widget': 'monetary', 'display_currency': website_sale_order.currency_id}"/>
                        </td>
                    </tr>


                </table>
            </div>
        </xpath>

    </template>

    <!--Checkout Navigation-->
    <template id="navigation_buttons_extended" inherit_id="website_sale.navigation_buttons"
              name="Navigation buttons extended">
        <xpath expr="//div[@t-attf-class]" position="replace">
            <t t-set="step_specific_values" t-value="website._get_checkout_steps(xmlid)"/>
            <div t-attf-class="#{_container_classes} d-flex #{_form_send_navigation and 'flex-column flex-lg-row align-items-lg-center' or 'flex-column'} mb-1 mb-lg-0 pt-2">
                <t t-if="website_sale_order and website_sale_order.website_order_line">
                    <t t-if="xmlid == 'website_sale.payment'">
                        <div t-if="not errors and not website_sale_order.amount_total"
                             name="o_website_sale_free_cart">
                            <form name="o_wsale_confirm_order"
                                  class="d-flex flex-column"
                                  target="_self"
                                  action="/shop/payment/validate"
                                  method="post">
                                <input type="hidden"
                                       name="csrf_token"
                                       t-att-value="request.csrf_token()"
                                       t-nocache="The csrf token must always be up to date."/>
                                <t t-if="not hide_payment_button" t-call="payment.submit_button">
                                    <t t-set="submit_button_label">Confirm Order</t>
                                </t>
                            </form>
                        </div>
                        <t t-elif="not hide_payment_button" t-call="payment.submit_button"/>
                    </t>
                    <t t-else="">
                        <a role="button" name="website_sale_main_button"
                           t-attf-class="#{_cta_classes} btn btn-primary #{not website_sale_order._is_cart_ready() and 'disabled'} #{_form_send_navigation and 'order-lg-3 w-100 w-lg-auto ms-lg-auto' or 'w-100'}"
                           t-att-href="step_specific_values['main_button_href']">
                            <t t-out="step_specific_values['main_button']"/>
                            <i class="fa fa-angle-right ms-2 fw-light"/>
                        </a>
                    </t>
                </t>
                <div t-if="not hide_payment_button"
                     t-attf-class="position-relative #{_form_send_navigation and 'd-flex d-lg-none' or 'd-flex'} w-100 justify-content-center align-items-center my-2 opacity-75">
                    <hr class="w-100"/>
                    <span class="px-3">or</span>
                    <hr class="w-100"/>
                </div>
                <a t-att-href="step_specific_values['back_button_href']" class="text-center">
                    <i class="fa fa-angle-left me-2 fw-light"/>
                    <t t-out="step_specific_values['back_button']"/>
                </a>
            </div>
        </xpath>

    </template>

    <!--Change the placeholder text-->
    <template id="custom_sale_coupon_form" inherit_id="website_sale.coupon_form">
        <!--        <xpath expr="//form[@name='coupon_code']//input[@name='promo']" position="attributes">-->
        <!--            <attribute name="placeholder">Discount code...</attribute>-->
        <!--        </xpath>-->

        <xpath expr="//form" position="replace">
            <form t-attf-action="/shop/pricelist#{redirect and '?r=' + redirect or ''}"
                  method="post" name="coupon_code">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"
                       t-nocache="The csrf token must always be up to date."/>
                <div class="input-group w-100 my-2">
                    <input name="promo" class="form-control" type="text" placeholder="Discount code..."
                           t-att-value="website_sale_order.pricelist_id.code or None"/>
                    <a href="#" role="button" class="btn btn-secondary a-submit">Apply</a>
                </div>
            </form>
        </xpath>
    </template>

    <!--Checkout summary in checkout page-->
    <template id="o_wsale_total_accordion_item_extended" inherit_id="website_sale.checkout_layout"
              name="Checkout Total Summary Extended">
        <xpath expr="//div[@id='o_wsale_total_accordion_item']" position="replace">
            <div id="o_wsale_total_accordion_item" class="accordion-item p-lg-4 border-0">
                <div class="accordion-header d-block align-items-center mb-2 oe_checkout_summary">
                    <button class="accordion-button px-0 "
                            data-bs-toggle="collapse"
                            data-bs-target="#o_wsale_accordion_item"
                            aria-expanded="false"
                            aria-controls="o_wsale_accordion_item">
                        <div class="d-flex flex-wrap">
                            <b class="w-100 mb-2d">Order summary</b>
                            <span t-out="str(website_sale_order.cart_quantity)"/>
                            &amp;nbsp;item(s)
                            <span id="amount_total_summary"
                                  class="monetary_field ms-1 d-none"
                                  t-field="website_sale_order.amount_total"
                                  t-options='{"widget": "monetary", "display_currency": website_sale_order.currency_id}'/>
                        </div>
                    </button>
                </div>
                <div name="cart_summary_info" t-if="not website_sale_order or not website_sale_order.website_order_line"
                     class="alert alert-info">
                    Your cart is empty!
                </div>
                <div id="o_wsale_accordion_item" class="accordion-collapse collapse mb-2 mb-lg-0 show"
                     data-bs-parent="#o_wsale_total_accordion">
                    <div t-att-class="len(website_sale_order.website_order_line) &gt; 3 and 'o_wsale_scrollable_table mt-n4 me-n4 pt-2   pe-4'">
                        <table t-if="website_sale_order and website_sale_order.website_order_line"
                               class="table accordion-body mb-0"
                               id="cart_products">
                            <tbody>
                                <tr t-foreach="website_sale_order.website_order_line" t-as="line"
                                    t-att-class="line_last and 'border-transparent'">
                                    <t t-set="o_cart_sum_padding_top"
                                       t-value="'pt-3' if line_size &gt; 1 and not line_first else 'pt-0'"/>
                                    <td t-if="not line.product_id" colspan="2"/>
                                    <t t-else="">
                                        <td t-attf-class="td-img ps-0 #{o_cart_sum_padding_top}">
                                            <span t-if="line._is_not_sellable_line() and line.product_id.image_128">
                                                <img t-att-src="image_data_uri(line.product_id.image_128)"
                                                     class="o_image_64_max img rounded" t-att-alt="line.name_short"/>
                                            </span>
                                            <span t-else="" t-field="line.product_id.image_128"
                                                  t-options="{'widget': 'image', 'qweb_img_responsive': False, 'class': 'o_image_64_max rounded'}"/>
                                        </td>
                                        <td t-attf-class="#{o_cart_sum_padding_top} td-product_name td-qty w-100"
                                            name='website_sale_cart_summary_product_name'>
                                            <div>
                                                <t t-out="int(line.product_uom_qty)"/>
                                                <t t-if="line._get_shop_warning(clear=False)">
                                                    <i class="fa fa-warning text-warning"
                                                       role="img"
                                                       t-att-title="line._get_shop_warning()"
                                                       aria-label="Warning"/>
                                                </t>
                                                x
                                                <t t-out="line.product_id.product_tmpl_id.name"/>

                                                <!--Variants of product-->
                                                <t t-foreach="line.product_id.product_template_attribute_value_ids"
                                                   t-as="attr_value">
                                                    <br/>
                                                    <small class="mb-0">
                                                        <span class="fw-600">
                                                            <t t-esc="attr_value.attribute_id.website_display_name"/>:
                                                        </span>
                                                        <t t-esc="attr_value.name.title()"/>
                                                    </small>
                                                </t>

                                            </div>
                                        </td>
                                    </t>
                                    <td t-attf-class="#{o_cart_sum_padding_top} td-price pe-0 text-end"
                                        name="website_sale_cart_summary_line_price">
                                        <t t-call="website_sale.cart_product_price">
                                            <t t-set="product_price"
                                               t-value="line._get_cart_display_price()"/>
                                        </t>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <t t-if='website_sale_order'>
                        <t t-set='warning'
                           t-value='website_sale_order._get_shop_warning(clear=False)'/>
                        <div t-if='warning' class="alert alert-warning"
                             role="alert">
                            <strong>Warning!</strong>
                            <t t-esc='website_sale_order._get_shop_warning()'/>
                        </div>
                    </t>
                </div>
                <t t-call="website_sale.total">
                    <t t-set="_cart_total_classes" t-valuef="border-top pt-3"/>
                </t>
                <div t-if="show_navigation_button"
                     class="o_cta_navigation_container position-absolute position-lg-static start-0 bottom-0 col-12">
                    <t t-call="website_sale.navigation_buttons"/>
                </div>
            </div>
        </xpath>
    </template>

    <template id="taxes" name="Custom Taxex">

        <!--id is used to reflect the updated tax price which is updated from js-->
        <span id="updated_tax_amount">
            <!--Initial tax price-->
            $<span t-out="'{0:,.2f}'.format(website_sale_order.amount_tax)"/>
        </span>

    </template>

</odoo>