<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="custom_portal_my_details_fields" name="Custom My Details Fields">
        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>


        <div t-if="error_message" class="alert alert-danger" role="alert">
            <div class="col-lg-12">
                <t t-foreach="error_message" t-as="err">
                    <t t-esc="err"/>
                    <br/>
                </t>
            </div>
        </div>
        <div t-if="not partner_can_edit_vat" class="col-12 d-none d-xl-block mb-2">
            <small class="form-text text-muted">Company name and country can not be
                changed once document(s) have been issued for your account.<br/>Please contact us
                directly for that operation.
            </small>
        </div>
        <div t-attf-class="mb-3 #{error.get('name') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label" for="name">Name</label>-->
            <input type="text" name="name" placeholder="Name"
                   t-attf-class="form-control #{error.get('name') and 'is-invalid' or ''}"
                   t-att-value="name or partner.name"/>
        </div>
        <div t-attf-class="mb-3 #{error.get('email') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label" for="email">Email</label>-->
            <input type="email" name="email" placeholder="Email"
                   t-attf-class="form-control #{error.get('email') and 'is-invalid' or ''}"
                   t-att-value="email or partner.email"/>
        </div>

        <div class="clearfix"/>
        <div t-attf-class="mb-1 #{error.get('company_name') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label label-optional" for="company_name">Company Name</label>-->
            <!-- The <input> use "disabled" attribute to avoid sending an unauthorized value on form
            submit.
                 The user might not have rights to change company_name but should still be able to see it.
            -->
            <input type="text" name="company_name" placeholder="Company Name"
                   t-attf-class="form-control #{error.get('company_name') and 'is-invalid' or ''}"
                   t-att-value="company_name or partner.commercial_company_name"
                   t-att-disabled="None if partner_can_edit_vat else '1'"/>
            <small t-if="not partner_can_edit_vat" class="form-text text-muted d-block d-xl-none">
                Changing company name is not allowed once document(s) have been issued for your
                account. Please contact us directly for this operation.
            </small>
        </div>
        <!--        <div t-attf-class="mb-1 #{error.get('vat') and 'o_has_error' or ''} col-xl-6 d-none">-->
        <!--            <label class="col-form-label label-optional" for="vat">VAT Number</label>-->
        <!--            &lt;!&ndash; The <input> use "disabled" attribute to avoid sending an unauthorized value on form-->
        <!--            submit.-->
        <!--                 The user might not have rights to change company_name but should still be able to see it.-->
        <!--            &ndash;&gt;-->
        <!--            <input type="text" name="vat"-->
        <!--                   t-attf-class="form-control #{error.get('vat') and 'is-invalid' or ''}"-->
        <!--                   t-att-value="vat or partner.vat"-->
        <!--                   t-att-disabled="None if partner_can_edit_vat else '1'"/>-->
        <!--            <small t-if="not partner_can_edit_vat" class="form-text text-muted d-block d-xl-none">Changing-->
        <!--                VAT number is not allowed once document(s) have been issued for your account. Please-->
        <!--                contact us directly for this operation.-->
        <!--            </small>-->
        <!--        </div>-->
        <div t-attf-class="mb-3 #{error.get('phone') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label" for="phone">Phone</label>-->
            <input type="tel" name="phone" placeholder="Phone"
                   t-attf-class="form-control #{error.get('phone') and 'is-invalid' or ''}"
                   t-att-value="phone or partner.phone"/>
        </div>

        <!--For Autocomplete address-->

        <input type="hidden" name="required_fields"
               t-att-value="'state_id,country_id'" disabled="True"/>

        <div class="clearfix"/>

        <!--Street-->
        <div id="div_street" t-attf-class="mb-3 #{error.get('street') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label" for="street">Street</label>-->
            <input type="text" name="street" placeholder="Street"
                   t-attf-class="form-control #{error.get('street') and 'is-invalid' or ''}"
                   t-att-value="street or partner.street"/>
        </div>

        <!--Street 2-->
        <div id="div_street2" t-attf-class="mb-3 #{error.get('street2') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label" for="street2">Street 2</label>-->
            <input type="text" name="street2" placeholder="Street 2"
                   t-attf-class="form-control #{error.get('street2') and 'is-invalid' or ''}"
                   t-att-value="street2 or partner.street2"/>
        </div>

        <!--City-->
        <div id="div_city" t-attf-class="mb-3 #{error.get('city') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label" for="city">City</label>-->
            <input type="text" name="city" placeholder="City"
                   t-attf-class="form-control #{error.get('city') and 'is-invalid' or ''}"
                   t-att-value="city or partner.city"/>
        </div>

        <!--Postcode-->
        <div t-attf-class="mb-3 #{error.get('zip') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label label-optional" for="zipcode">Postcode</label>-->
            <input type="text" name="zipcode" placeholder="Postcode"
                   t-attf-class="form-control #{error.get('zip') and 'is-invalid' or ''}"
                   t-att-value="zipcode or partner.zip"/>
        </div>


        <!--Country-->
        <div t-attf-class="mb-3 #{error.get('country_id') and 'o_has_error' or ''} col-xl-6">
            <select id="o_country_id" name="country_id" class="form-select required"
                    t-att-disabled="None if partner_can_edit_vat else '1'">
                <option value="">Select Country</option>
                <t t-foreach="countries" t-as="c">
                    <option t-att-value="c.id" t-att-selected="c.code == 'AU'"
                            t-att-code="c.code">
                        <t t-esc="c.name"/>
                    </option>
                </t>
            </select>
        </div>

        <!--States-->
        <t t-set="country_states" t-value="env['res.country.state'].search([('country_id','=',13)])"/>

        <div id="div_state" t-attf-class="mb-3 #{error.get('state_id') and 'o_has_error' or ''} col-xl-6">
            <!--            <label class="col-form-label label-optional" for="state_id">State / Province</label>-->
            <select id="o_state_id" name="state_id"
                    t-attf-class="form-select #{error.get('state_id') and 'is-invalid' or ''} required">
                <option value="None">Select State / Territory</option>
                <t t-foreach="country_states" t-as="state">
                    <option t-att-value="state.id"
                            t-att-selected="state.id == state_id if state_id else state.id == partner.state_id.id"
                            t-att-data-code="state.code">
                        <t t-esc="state.name"/>
                    </option>

                </t>

            </select>
        </div>

    </template>


    <!--Custom User My Detail Fields-->
    <template id="portal_my_details_extended" inherit_id="portal.portal_my_details"
              name="Custom Portal My Details">

        <xpath expr="//t[@t-call='portal.portal_layout']" position="replace">
            <t t-call="portal.portal_layout">
                <t t-set="additional_title">Contact Details</t>

                <div class="oe_cart">
                    <form action="/my/account" method="post" class="checkout_autoformat">
                        <div class="row ">
                            <div class="col-lg-8">
                                <div class="row">
                                    <t t-call="portal.portal_my_details_fields"/>
                                    <input type="hidden" name="redirect" t-att-value="redirect"/>
                                </div>
                                <div class="clearfix text-end mb-6">
                                    <a href="/my/" class="btn btn-light me-2">
                                        Discard
                                    </a>
                                    <button type="submit" class="btn btn-primary float-end">
                                        Save
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </t>
        </xpath>

    </template>


    <template id="extend_portal_my_details_fields" inherit_id="portal.portal_my_details_fields">
        <xpath expr="." position="replace">
            <!-- Custom Fields -->
            <t t-call="modula_web.custom_portal_my_details_fields"/>
        </xpath>
    </template>

    <!--    Google address autocomplete-->
    <template id="modula_web_address_with_autocomplete_1" inherit_id="modula_web.custom_portal_my_details_fields">
        <xpath expr="//input[@name='street']" position="attributes">
            <attribute name="t-att-data-autocomplete-enabled">
                1 if website.has_google_places_api_key() else 0
            </attribute>
        </xpath>
    </template>


</odoo>