<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!--Custom User Login-->
    <template id="custom_login" inherit_id="web.login" name="Custom Login">


        <xpath expr="//t[@t-call='web.login_layout']" position="replace">
            <t t-call="web.login_layout">

                <!--Title-->
                <div class="mb-3 text-center oe_login_title">
                    <h2>Log in</h2>
                </div>

                <!--Subtitle-->
                <div class="mb-1 text-center oe_login_subtitle">

                    <t t-set="query_str" t-value="(request.httprequest.query_string or b'').decode('utf-8')"/>
                    <t t-if="'redirect=/shop/checkout' in query_str">
                        <p class="lead mb-1">Ready to checkout? Log in or create an account to finalise your order
                            swiftly
                            and securely.
                        </p>
                    </t>
                    <t t-else="">
                        <p class="lead mb-1">Welcome back! Log in to continue your seamless furniture shopping
                            experience.
                        </p>
                    </t>

                </div>

                <!--Switch user-->
                <owl-component t-if="not login" name="web.user_switch"/>

                <!--Login Form-->
                <form t-attf-class="oe_login_form #{'' if login else 'd-none'}" role="form"
                      t-attf-action="/web/login"
                      method="post" onsubmit="this.action = '/web/login' + location.hash">
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                    <!--Database Selection-->
                    <div class="mb-1" t-if="databases and len(databases) &gt; 1">
                        <!--                    <label for="db" class="col-form-label">Database</label>-->
                        <div class="input-group">
                            <input type="text" name="db" t-att-value="request.db" id="db"
                                   class="form-control form-control-md"
                                   required="required" readonly="readonly"/>
                            <a role="button" href="/web/database/selector" class="btn btn-secondary">Select
                                <i class="fa fa-database" role="img" aria-label="Database" title="Database"></i>
                            </a>
                        </div>
                    </div>

                    <!--Email-->
                    <div class="mb-1 field-login">
                        <!--                    <label for="login" class="form-label d-flex justify-content-between">Email</label>-->
                        <input type="text" placeholder="Email" name="login" t-att-value="login" id="login"
                               class="form-control form-control-md" required="required" autocapitalize="off"
                               autocomplete="username"/>
                    </div>

                    <!--Password-->
                    <div class="mb-1">
                        <!--                    <label for="password" class="form-label d-flex justify-content-between">Password</label>-->
                        <input type="password" placeholder="Password" name="password" id="password"
                               class="form-control form-control-md"
                               required="required" autocomplete="current-password"
                               t-att-autofocus="'autofocus' if login else None" maxlength="4096"/>
                    </div>

                    <p class="alert alert-danger" t-if="error" role="alert">
                        <t t-esc="error"/>
                    </p>
                    <p class="alert alert-success" t-if="message" role="status">
                        <t t-esc="message"/>
                    </p>

                    <div class="oe_login_buttons text-center gap-1 d-grid mb-1 pt-1">
                        <button type="submit" class="btn btn-primary">Log in</button>

                        <!--login as superuser-->
                        <t t-if="debug">
                            <button type="submit" name="redirect" value="/web/become" class="btn btn-link btn-sm">
                                Log in as
                                superuser
                            </button>
                        </t>
                        <div class="o_login_auth"/>
                    </div>

                    <input type="hidden" name="type" value="password"/>
                    <input type="hidden" name="redirect" t-att-value="redirect"/>
                </form>
            </t>

        </xpath>

    </template>

    <!--Signup & Reset Password Links-->
    <template id="signup_reset_links" inherit_id="modula_web.custom_login" name="Custom Sign up - Reset Password">
        <xpath expr="//div[hasclass('oe_login_buttons')]" position="after">
            <p class="mt-2 mb-0 text-center">
                <a t-if="signup_enabled" class="btn btn-link btn-sm" t-attf-href="/web/signup?{{ keep_query() }}">
                    Don't have an account?
                </a>
            </p>
            <p class="text-center">
                <a t-if="reset_password_enabled" class="btn btn-link btn-sm" tabindex="1"
                   t-attf-href="/web/reset_password?{{ keep_query() }}">Reset Password
                </a>
            </p>
        </xpath>
    </template>

</odoo>