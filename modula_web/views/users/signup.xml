<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!--Custom User Signup-->
    <template id="custom_signup" inherit_id="auth_signup.signup" name="Custom signup">

        <xpath expr="//form" position="before">
            <div class="mb-3 text-center oe_login_title">
                <h2>Sign up</h2>
            </div>

            <div class="mb-1 text-center oe_login_subtitle">


                <t t-set="query_str" t-value="(request.httprequest.query_string or b'').decode('utf-8')"/>
                <t t-if="'redirect=%2Fshop%2Fcheckout%3Ftry_skip_step%3Dtrue' in query_str">
                    <p class="lead mb-1">New here? Create an account to checkout swiftly and securely.
                    </p>
                </t>
                <t t-else="">
                    <p class="lead mb-1">Join our community and enjoy personalised shopping, speedy checkouts, and
                        effortless order tracking.
                    </p>
                </t>

            </div>
        </xpath>

        <xpath expr="//div[contains(@class, 'oe_login_buttons')]" position="replace">
            <div class="text-center oe_login_buttons d-grid pt-1">
                <button type="submit" class="btn btn-primary">
                    <span class="ms-1">Sign up</span>
                </button>
                <p class="mt-2 mb-0 text-center">
                    <a t-attf-href="/web/login?{{ keep_query() }}" class="btn btn-link btn-sm" role="button">Already
                        have an account?
                    </a>
                </p>
                <div class="o_login_auth"/>
            </div>
        </xpath>

    </template>


    <template id="signup_fields_extended" inherit_id="auth_signup.fields"
              name="Auth Signup/ResetPassword form fields extended">

        <!--Remove the email field and keep it after name-->
        <xpath expr="//div[contains(@class, 'field-login')]" position="replace"/>

        <xpath expr="//div[contains(@class, 'field-name')]" position="replace">
            <div class="mb-1 field-name">
                <input type="text" name="name" t-att-value="name" id="name" class="form-control form-control-md"
                       autofocus="autofocus" placeholder="Name" required="required"
                       t-att-readonly="'readonly' if only_passwords else None"/>
            </div>

            <div class="mb-1 field-login">
                <input type="text" placeholder="Email" name="login" t-att-value="login" id="login"
                       class="form-control form-control-md"
                       autocapitalize="off" required="required"
                       t-att-readonly="'readonly' if only_passwords else None"/>
            </div>
        </xpath>

        <xpath expr="//div[contains(@class, 'field-password')]" position="replace">
            <div class="mb-1 field-password">
                <input type="password" placeholder="Password" name="password" id="password"
                       class="form-control form-control-md"
                       required="required" t-att-autofocus="'autofocus' if only_passwords else None"/>
            </div>
        </xpath>

        <xpath expr="//div[contains(@class, 'field-confirm_password')]" position="replace">
            <div class="mb-1 field-confirm_password">
                <input type="password" placeholder="Confirm Password" name="confirm_password" id="confirm_password"
                       class="form-control form-control-md"
                       required="required"/>
            </div>
        </xpath>


    </template>


    <!--Signup Fields-->
    <!--    <template id="signup_fields" name="Custom Auth Signup/ResetPassword form fields">-->

    <!--        <div class="mb-1 field-name">-->
    <!--            &lt;!&ndash;            <label for="name" class="form-label d-flex justify-content-between">Name</label>&ndash;&gt;-->
    <!--            <input type="text" name="name" t-att-value="name" id="name" class="form-control form-control-md"-->
    <!--                   autofocus="autofocus" placeholder="Name" required="required"-->
    <!--                   t-att-readonly="'readonly' if only_passwords else None"-->
    <!--            />-->
    <!--        </div>-->

    <!--        <div class="mb-1 field-login">-->
    <!--            &lt;!&ndash;            <label for="login" class="form-label d-flex justify-content-between">Email</label>&ndash;&gt;-->
    <!--            <input type="text" placeholder="Email" name="login" t-att-value="login" id="login"-->
    <!--                   class="form-control form-control-md"-->
    <!--                   autocapitalize="off" required="required" t-att-readonly="'readonly' if only_passwords else None"/>-->
    <!--        </div>-->

    <!--        <div class="mb-1 field-password">-->
    <!--            &lt;!&ndash;            <label for="password" class="form-label d-flex justify-content-between">Password</label>&ndash;&gt;-->
    <!--            <input type="password" placeholder="Password" name="password" id="password"-->
    <!--                   class="form-control form-control-md"-->
    <!--                   required="required" t-att-autofocus="'autofocus' if only_passwords else None"/>-->
    <!--        </div>-->

    <!--        <div class="mb-1 field-confirm_password">-->
    <!--            &lt;!&ndash;            <label for="confirm_password" class="form-label d-flex justify-content-between">Confirm Password</label>&ndash;&gt;-->
    <!--            <input type="password" placeholder="Confirm Password" name="confirm_password" id="confirm_password"-->
    <!--                   class="form-control form-control-md"-->
    <!--                   required="required"/>-->
    <!--        </div>-->
    <!--    </template>-->


</odoo>