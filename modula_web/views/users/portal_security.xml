<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="portal_my_home_extended" inherit_id="portal.portal_my_home" name="Custom Portal My Home">
        <xpath expr="//t[@t-call='portal.portal_layout']" position="replace">
            <t t-call="portal.portal_layout">
                <t t-set="my_details" t-value="True"/>
                <div class="o_portal_my_home">
                    <div class="oe_structure" id="oe_structure_portal_my_home_1"/>
                    <div class="o_portal_docs row g-2">
                        <div t-if="portal_alert_category_enable" class="o_portal_category row g-2 mt-3"
                             id="portal_alert_category"/>
                        <div t-if="portal_client_category_enable" class="o_portal_category row g-2 mt-3"
                             id="portal_client_category"/>
                        <div t-if="portal_service_category_enable"
                             class="o_portal_category row g-2 mt-3" id="portal_service_category"/>
                        <div t-if="portal_vendor_category_enable" class="o_portal_category row g-2 mt-3"
                             id="portal_vendor_category"/>
                        <div class="o_portal_category row g-2 mt-3" id="portal_common_category">
                            <t t-call="portal.portal_docs_entry" t-if="False"/>
                            <t t-call="portal.portal_docs_entry">
                                <t t-set="icon" t-value="'/portal/static/src/img/portal-connection.svg'"/>
                                <t t-set="title">Security</t>
                                <t t-set="text">Manage your passwords</t>
                                <t t-set="url" t-value="'/my/security'"/>
                                <t t-set="config_card" t-value="True"/>
                            </t>
                        </div>
                        <div
                                class="o_portal_doc_spinner spinner-border text-o-color-2 align-self-center mt-5"/>
                    </div>
                </div>
                <div class="oe_structure" id="oe_structure_portal_my_home_2"/>
            </t>

        </xpath>
    </template>


    <template id="portal_my_security_extended" inherit_id="portal.portal_my_security" name="Custom Portal My Security">
        <xpath expr="//t[@t-call='portal.portal_layout']" position="replace">
            <t t-call="portal.portal_layout">
                <div class="o_portal_security_body w-md-75 w-lg-50 pb-5">
                    <t t-set="additional_title">Security</t>
                    <t t-set="no_breadcrumbs" t-value="1"/>
                    <div class="alert alert-danger" role="alert" t-if="get_error(errors)">
                        <t t-esc="errors"/>
                    </div>
                    <div class="d-flex gap-2 my-3">
                        <a href="/my/" title="Go Back" class="btn btn-light px-2">
                            <i class="oi oi-chevron-left"/>
                        </a>
                        <h3 class="my-0">Security</h3>

                    </div>
                    <section name="portal_change_password">
                        <h4 class="mb-1">Change Password</h4>
                        <t t-set="path">password</t>
                        <div class="alert alert-success" role="alert"
                             t-if="success and success.get('password')">
                            Password Updated!
                        </div>
                        <div class="alert alert-danger" role="alert"
                             t-if="get_error(errors, 'password')">
                            <t t-esc="errors['password']"/>
                        </div>
                        <form action="/my/security" method="post" class="oe_reset_password_form">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            <input type="hidden" name="op" value="password"/>
                            <div class="mb-3">
                                <label for="current">Password:</label>
                                <input type="password"
                                       t-attf-class="form-control {{ 'is-invalid' if get_error(errors, 'password.old') else '' }}"
                                       id="current" name="old"
                                       autocomplete="current-password" required="required"/>
                                <div class="invalid-feedback">
                                    <t t-esc="get_error(errors, 'password.old')"/>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="new">New Password:</label>
                                <input type="password"
                                       t-attf-class="form-control {{ 'is-invalid' if get_error(errors, 'password.new1') else '' }}"
                                       id="new" name="new1"
                                       autocomplete="new-password" required="required"/>
                                <div class="invalid-feedback">
                                    <t t-esc="get_error(errors, 'password.new1')"/>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="new2">Verify New Password:</label>
                                <input type="password"
                                       t-attf-class="form-control {{ 'is-invalid' if get_error(errors, 'password.new2') else '' }}"
                                       id="new2" name="new2"
                                       autocomplete="new-password" required="required"/>
                                <div class="invalid-feedback">
                                    <t t-esc="get_error(errors, 'password.new2')"/>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-light">Change Password</button>
                        </form>
                    </section>


                    <section name="portal_revoke_all_devices_popup" t-if="request.env.user.is_system">
                        <h4>Revoke All Sessions</h4>
                        <t t-set="path"/>
                        <button type="button" class="btn btn-light"
                                id="portal_revoke_all_sessions_popup">
                            Log out from all devices
                        </button>
                    </section>


                    <section t-if="debug and allow_api_keys and request.env.user.is_system">
                        <h4>Developer API Keys
                            <a
                                    href="https://www.odoo.com/documentation/master/developer/misc/api/external_api.html#api-keys"
                                    target="_blank">
                                <i title="Documentation" class="fa fa-fw o_button_icon fa-info-circle"></i>
                            </a>
                        </h4>
                        <div>
                            <table class="table o_main_table">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>Scope</th>
                                        <th>Added On</th>
                                        <th>Expiration Date</th>
                                        <th/>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="request.env.user.api_key_ids" t-as="key">
                                        <tr>
                                            <td>
                                                <span t-field="key.name"/>
                                            </td>
                                            <td>
                                                <span t-field="key.scope"/>
                                            </td>
                                            <td>
                                                <span t-field="key.create_date"/>
                                            </td>
                                            <td>
                                                <span t-field="key.expiration_date"/>
                                            </td>
                                            <td>
                                                <i
                                                        class="fa fa-trash text-danger o_portal_remove_api_key"
                                                        type="button" t-att-id="key.id"/>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-light o_portal_new_api_key">New API Key</button>
                        </div>
                    </section>


                    <section name="portal_deactivate_account" groups="base.group_portal"
                             t-if="request.env.user.is_system">
                        <h4>Delete Account</h4>
                        <t t-set="deactivate_error" t-value="get_error(errors, 'deactivate')"/>
                        <button class="btn btn-light" data-bs-toggle="modal"
                                data-bs-target="#portal_deactivate_account_modal">
                            Delete Account
                        </button>
                        <div t-attf-class="modal #{'show d-block' if open_deactivate_modal else ''}"
                             id="portal_deactivate_account_modal" tabindex="-1" role="dialog">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header bg-danger">
                                        <h5 class="modal-title">Are you sure you want to do this?</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <form action="/my/deactivate_account" method="post"
                                          class="modal-body"
                                          id="portal_deactivate_account_form">
                                        <div>
                                            <div class="alert alert-danger"
                                                 t-esc="get_error(errors, 'deactivate.other')"/>
                                            <p class="text-muted">Disable your account, preventing any
                                                further login.
                                                <br/>
                                                <b>
                                                    <i class="fa fa-exclamation-triangle text-danger"></i>
                                                    This action cannot be undone.
                                                </b>
                                            </p>
                                            <hr/>
                                            <p>1. Enter your password to confirm you own this account</p>
                                            <input name="password" type="password" required="1"
                                                   t-attf-class="form-control #{'is-invalid' if deactivate_error == 'password' else ''}"
                                                   placeholder="Password"/>
                                            <div t-if="deactivate_error == 'password'"
                                                 class="invalid-feedback">
                                                Wrong password.
                                            </div>
                                            <hr/>
                                            <p>2. Confirm you want to delete your account by copying
                                                down your login (<t t-esc="env.user.login"/>).
                                            </p>
                                            <input name="validation" type="text" required="1"
                                                   t-attf-class="form-control #{'is-invalid' if deactivate_error == 'validation' else ''}"/>
                                            <div t-if="deactivate_error == 'validation'"
                                                 class="invalid-feedback">You should enter "<t
                                                    t-esc="env.user.login"/>" to validate your action.
                                            </div>
                                            <div class="d-flex flex-row align-items-center">
                                                <input type="checkbox" name="request_blacklist"
                                                       id="request_blacklist" checked="1"/>
                                                <label for="request_blacklist"
                                                       class="ms-2 mw-100 fw-normal mt-3">
                                                    Put my email and phone in a block list to make sure
                                                    I'm never contacted again
                                                </label>
                                            </div>
                                        </div>
                                        <input type="hidden" name="csrf_token"
                                               t-att-value="request.csrf_token()"/>
                                    </form>
                                    <div class="modal-footer justify-content-start">
                                        <input type="submit" class="btn btn-danger"
                                               form="portal_deactivate_account_form"
                                               value="Delete Account"/>
                                        <button type="button" class="btn" data-bs-dismiss="modal">
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                </div>
            </t>
        </xpath>
    </template>
</odoo>