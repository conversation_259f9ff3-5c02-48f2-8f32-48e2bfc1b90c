<?xml version="1.0" encoding="utf-8"?>
<odoo>


<!--    &lt;!&ndash;    Portal Layout&ndash;&gt;-->
<!--    <template id="custom_portal_layout" inherit_id="portal.portal_layout" name="Custom portal layout">-->
<!--        <xpath expr="//t[@t-call='portal.frontend_layout']" position="replace">-->

<!--            <t t-call="portal.frontend_layout">-->
<!--                <t t-set="is_portal" t-value="True"/>-->

<!--                <div id="wrap" class='o_portal_wrap'>-->


<!--                    <div class="container pt-3 pb-5">-->

<!--                        <div t-if="not no_breadcrumbs and not my_details and not breadcrumbs_searchbar"-->
<!--                             class="o_portal container">-->
<!--                            <div class="d-flex justify-content-between align-items-center flex-wrap">-->
<!--                                <t t-call="portal.portal_breadcrumbs"/>-->
<!--                                <t t-if="prev_record or next_record" t-call='portal.record_pager'/>-->
<!--                            </div>-->
<!--                        </div>-->


<!--                        <t t-if="my_details">-->
<!--                            <div class="wrapper col-12 d-flex flex-wrap justify-content-between align-items-center">-->
<!--                                <h3 class="my-3">My account</h3>-->
<!--                                <button-->
<!--                                        class="btn py-0 d-flex align-items-center gap-2 d-lg-none ms-auto"-->
<!--                                        data-bs-toggle="offcanvas"-->
<!--                                        data-bs-target="#accountOffCanvas">-->
<!--                                    <img class="o_avatar rounded"-->
<!--                                         t-att-src="image_data_uri(user_id.partner_id.avatar_1024)"-->
<!--                                         alt="Contact"/>-->
<!--                                </button>-->
<!--                            </div>-->
<!--                            <div class="row justify-content-between">-->
<!--                                <div t-attf-class="o_portal_content col-12 col-lg-8 mb-5">-->
<!--                                    <t t-out="0"/>-->
<!--                                </div>-->
<!--                                <div class="d-none d-lg-flex justify-content-end col-lg-4">-->
<!--                                    <t t-call="portal.side_content"/>-->
<!--                                </div>-->
<!--                                <div class="offcanvas offcanvas-start d-lg-none" id="accountOffCanvas">-->
<!--                                    <t t-call="portal.side_content">-->
<!--                                        <t t-set="isOffcanvas" t-value="true"/>-->
<!--                                    </t>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </t>-->
<!--                        <t t-else="">-->
<!--                            <t t-out="0"/>-->
<!--                        </t>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </t>-->

<!--        </xpath>-->
<!--    </template>-->


    <template id="side_content_extended" inherit_id="portal.side_content">
        <!--        Replaces the second div content-->
        <xpath expr="//div[2]" position="replace">

            <div t-attf-class="{{'offcanvas-body' if isOffcanvas else 'mt-3 mw-100'}}">
                <div class="d-flex justify-content-start align-items-start gap-3 mb-4">
                    <!--                    <img class="o_portal_contact_img rounded o_object_fit_cover"-->
                    <!--                         t-att-src="image_data_uri(user_id.partner_id.avatar_128)" alt="Contact"-->
                    <!--                         width="50"/>-->
                    <div class="d-flex flex-column justify-content-center">
                        <h5 class="mb-0" t-out="user_id.name"/>
                        <p class="mb-0 text-muted" t-out="user_id.company_name"/>
                    </div>
                </div>
                <div class="o_portal_my_details">
                    <div t-field="user_id.partner_id"
                         t-options='{"widget": "contact", "fields": ["email", "phone", "address"]}'/>
                </div>
                <a role="button" href="/my/account" class="btn btn-link p-0 mt-3">
                    <i class="fa fa-pencil"/>
                    Edit information
                </a>

                <div class="custom-url-section mt-3">


                    <a t-attf-href="/web/session/logout?redirect=/" id="o_logout">

                        <button class="nav-link o_nav_link_btn w-100 border text-center">
                            <i class="fa fa-sign-out" aria-hidden="true"></i>
                            Sign Out
                        </button>
                    </a>

                </div>
                <div name="portal_contact" class="o_my_contact mt-5" t-if="sales_user">
                    <t t-call="portal.portal_contact"/>
                </div>
            </div>

        </xpath>
    </template>
</odoo>