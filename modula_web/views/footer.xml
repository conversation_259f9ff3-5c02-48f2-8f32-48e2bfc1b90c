<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--Footer Template-->
    <template id="custom_footer_template" name="Footer Page Template" customize_show="False">
        <div id="footer" class="oe_structure oe_structure_solo o_cc5" t-ignore="true" t-if="not no_footer">
            <section class="s_text_block pt32 pb16" data-snippet="s_text_block" data-name="Text">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-4 pb16">
                            <h4>Modula Living</h4>
                            <p>
                                Beautifully flexible modular sofas, crafted for everyday comfort and lasting style.
                                Sophisticated design for simplified living.
                            </p>

                            <p class="contact">

                                <t t-set="company_phone" t-value="website.company_id.phone"/>
                                <a t-attf-href="tel:{{ company_phone }}"
                                   data-bs-original-title="Call Customer Service">
                                    <t t-esc="company_phone"/>
                                </a>
                                <!--                                <a href="tel:+61395356391">+61 ***********</a>-->
                            </p>

                            <p class="footer-social-links">
                                <a t-att-href="website.company_id.social_facebook" target="_blank">
                                    <i class="fa fa-facebook-square" aria-hidden="true"></i>
                                </a>

                                <a t-att-href="website.company_id.social_instagram" target="_blank">
                                    <i class="fa fa-instagram" aria-hidden="true"></i>
                                </a>

                                <a t-att-href="website.company_id.social_youtube" target="_blank">
                                    <i class="fa fa-youtube-play" aria-hidden="true"></i>
                                </a>
                            </p>
                        </div>
                        <div class="col-lg-4 pb16">
                            <h4>Shop</h4>


                            <div class="row">

                                <!-- Initialize the lists -->
                                <t t-set="submenu_part_1" t-value="[]"/>
                                <t t-set="submenu_part_2" t-value="[]"/>
                                <t t-set="submenu_count" t-value="0"/>

                                <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                    <t t-if="submenu.name in ['Sofas', 'Chairs', 'Tables']">
                                        <!-- Distribute child items between the two lists -->
                                        <t t-foreach="submenu.child_id" t-as="child">
                                            <t t-set="submenu_part_1" t-value="submenu_part_1 + [child]"/>
                                            <t t-set="submenu_part_2" t-value="submenu_part_2 + [child]"/>
                                            <t t-set="submenu_count" t-value="submenu_count + 1"/>
                                        </t>
                                    </t>

                                </t>

                                <t t-set="sofa_menu_size" t-value="(submenu_count + 1) // 2"/>


                                <div class=" col-6">

                                    <t t-foreach="submenu_part_1" t-as="child">
                                        <t t-if="child_index &lt; sofa_menu_size">
                                            <p>
                                                <a t-att-href="child._clean_url()">
                                                    <span t-esc="child.name"></span>
                                                </a>
                                            </p>
                                        </t>
                                    </t>
                                </div>

                                <div class="col-6">
                                    <t t-foreach="submenu_part_2" t-as="child">
                                        <t t-if="child_index &gt;= sofa_menu_size">
                                            <p>
                                                <a t-att-href="child._clean_url()">
                                                    <span t-esc="child.name"></span>
                                                </a>
                                            </p>
                                        </t>
                                    </t>
                                </div>
                            </div>

                        </div>
                        <div class="col-lg-2 col-6 pb16">
                            <h4>About</h4>
                            <p>
                                <a href="/showroom">Showroom</a>
                            </p>
                            <p>
                                <a href="/our-story">Our Story</a>
                            </p>
                            <p>
                                <a href="/contactus">Contact Us</a>
                            </p>
                            <p>
                                <a href="/blog">Blog</a>
                            </p>
                        </div>
                        <div class="col-lg-2 col-6 pb16">
                            <h4>Resources</h4>
                            <p>
                                <a href="/shipping-and-delivery">Shipping &amp; Delivery</a>
                            </p>
                            <p>
                                <a href="/returns-and-refunds">Returns &amp; Refunds</a>
                            </p>
                            <p>
                                <a href="/privacy-policy">Privacy Policy</a>
                            </p>
                            <p>
                                <a href="/care-and-maintenance">Care &amp; Maintenance</a>
                            </p>
                            <p>
                                <!--                                <a href="/terms-and-conditions">Terms &amp; Conditions</a>-->
                                <a href="/terms">Terms &amp; Conditions</a>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <small>Copyright &amp;copy; 2025 Modula Living. All Rights Reserved
                        </small>
                    </div>
                </div>
            </section>
        </div>
    </template>

</odoo>