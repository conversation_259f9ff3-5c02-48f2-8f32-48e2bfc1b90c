<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="header_template" name="Header Template">

        <header id="header" class="o_header_standard o_hoverable_dropdown">

            <!--Header for Desktop views-->
            <t t-call="modula_web.template_header_desktop"/>

            <!--Mobile header-->
            <t t-call="modula_web.template_header_mobile"/>
        </header>
    </template>

    <!--Desktop Header-->
    <template id="template_header_desktop" name="Template Header Desktop">
        <t t-call="website.navbar">
            <t t-set="is_current_page_home" t-value="request.httprequest.path == '/'"/>

            <t t-set="_navbar_classes"
               t-value="'d-none d-lg-block shadow-sm ' + ('navbar_transparent' if is_current_page_home else '')"/>

            <div id="o_main_nav" class="o_main_nav container-fluid pi-50">

                <!--Desktop Brand -->
                <div class="col-2 navbar-nav">
                    <!--                    <t t-call="website.placeholder_header_brand">-->
                    <!--                        <t t-set="_link_class" t-valuef="me-4"/>-->
                    <!--                    </t>-->

                    <a data-name="Navbar Logo" href="/" class="navbar-brand logo me-4">

                        <span role="img" aria-label="Logo of My Website" title="My Website" data-oe-model="website"
                              data-oe-id="1" data-oe-field="logo"
                              data-oe-type="image" data-oe-expression="website.logo">
                            <!--                            <img id="logo" src="/modula_web/static/src/img/website_logo_black.webp"-->
                            <!--                                 class="img img-fluid o_animate o_anim_fade_in"-->
                            <!--                                 alt="My Website" loading="lazy" style="animation-delay: 0s; animation-duration: 0s;"/>-->

                            <t t-if="is_current_page_home">
                                <img id="logo"
                                     src="/modula_web/static/src/img/website_logo_white.webp"
                                     class="img img-fluid"
                                     alt="My Website" loading="lazy"/>
                            </t>
                            <t t-else="">
                                <img id="logo"
                                     src="/modula_web/static/src/img/website_logo_black.webp"
                                     class="img img-fluid"
                                     alt="My Website" loading="lazy"/>
                            </t>


                        </span>
                    </a>
                </div>

                <!--Desktop Navbar Menu-->
                <div class="col-8 navbar-nav" style="justify-content: center">
                    <!--                    <t t-call="modula_web.custom_navbar_nav"></t>-->

                    <ul role="menu" id="top_menu" class="nav top_menu">
                        <t t-foreach="website.menu_id.child_id" t-as="submenu">
                            <t t-call="modula_web.custom_navbar">
                                <t t-set="item_class" t-valuef="nav-item"/>
                                <t t-set="link_class" t-valuef="nav-link"/>
                            </t>
                        </t>
                    </ul>
                </div>

                <!--Desktop Extra Menu-->
                <div class="col-2">
                    <t t-call="modula_web.custom_navbar_extra_menu"></t>
                </div>
            </div>
        </t>
    </template>

    <!--Desktop Navbar Menu-->
    <template id="custom_navbar" name="Custom Submenu">

        <t t-set="show_dropdown"
           t-value="(submenu.is_mega_menu and submenu.is_visible) or submenu.child_id.filtered(lambda menu: menu.is_visible)"/>

        <!--Simple Menu-->
        <li t-if="submenu.is_visible and not (submenu.child_id or submenu.is_mega_menu)"
            t-attf-class="#{item_class or ''}" role="presentation">
            <a t-att-href="submenu._clean_url()"
               t-attf-class="#{link_class or ''} #{submenu._is_active() and 'active'} hover-underline"
               role="menuitem"
               t-ignore="true"
               t-att-target="'_blank' if submenu.new_window else None">
                <span class="hover-underline-text" t-field="submenu.name"/>
            </a>
        </li>

        <!--Menu with sub child -->
        <t t-elif="show_dropdown">
            <!--            <t t-call="modula_web.mega_dropdown"/>-->
            <t t-call="modula_web.simple_dropdown"/>
        </t>


        <!--        <li t-elif="show_dropdown"-->
        <!--            t-attf-class="mega-nav-item #{item_class or ''} dropdown #{submenu.is_mega_menu and 'position-static'} simple_dropdown has-submenu"-->
        <!--            role="presentation">-->
        <!--            <a t-attf-class="#{link_class or ''} dropdown-toggle #{submenu.is_mega_menu and 'o_mega_menu_toggle'} #{submenu._is_active() and 'active'} #{dropdown_toggler_classes}"-->
        <!--               data-bs-toggle="dropdown" href="#" t-att-data-bs-display="'static' if submenu.is_mega_menu else None"-->
        <!--               role="menuitem">-->
        <!--                <span t-field="submenu.name"/>-->
        <!--            </a>-->
        <!--            <t t-if="submenu.is_mega_menu">-->
        <!--                <div t-if="not is_mobile"-->
        <!--                     t-attf-class="dropdown-menu o_mega_menu #{submenu.mega_menu_classes}"-->
        <!--                     data-name="Mega Menu"-->
        <!--                     t-field="submenu.mega_menu_content"-->
        <!--                     role="menuitem"/>-->
        <!--            </t>-->
        <!--            <ul t-else="" t-attf-class="dropdown-menu #{dropdown_menu_classes}" role="menu">-->
        <!--                <t t-foreach="submenu.child_id" t-as="submenu">-->
        <!--                    <t t-call="website.submenu">-->
        <!--                        <t t-set="item_class" t-value="None"/>-->
        <!--                        <t t-set="link_class" t-valuef="dropdown-item"/>-->
        <!--                    </t>-->
        <!--                </t>-->
        <!--            </ul>-->
        <!--        </li>-->

    </template>

    <!--Desktop Extra Menus-->
    <template id="custom_navbar_extra_menu" name="Custom Navbar Extra Menu">
        <ul class="navbar-nav align-items-center gap-3 flex-shrink-0 justify-content-end ps-3">

            <!-- Search bar-->
            <t t-call="modula_web.placeholder_header_search_box"
               t-nocache="The searchbox should not cache previous searches.">
                <t t-set="_layout" t-valuef="modal"/>
                <t t-set="_input_classes" t-valuef="border border-end-0 p-3"/>
                <t t-set="_submit_classes" t-valuef="border border-start-0 px-4 bg-o-color-4"/>
                <t t-set="_button_classes" t-valuef=" text-reset"/>
            </t>


            <!--Wishlist Link-->
            <t t-if="'product.wishlist' in env">

                <t t-call="modula_web.custom_header_wishlist_link">
                    <t t-set="_icon" t-value="True"/>
                    <t t-set="_link_class"
                       t-value="' btn position-relative rounded-circle border-0 p-1 text-reset'"/>
                    <t t-set="_badge_class" t-value="'position-absolute top-0 end-0 mt-n1 me-n1'"/>
                </t>

            </t>


            <!--User Login-->
            <li t-attf-class="nav-item">
                <t t-set="is_connected" t-value="not user_id._is_public()"/>
                <a href="/my/home" class="btn border-none p-1 lh-1  text-reset o_not_editable" t-if="is_connected">
                    <t t-call="modula_web.user_icon"/>
                    <!--                    <img src="/modula_web/static/src/svg/user-icon.svg" alt="User"-->
                    <!--                         width="25" height="25"/>-->
                </a>
                <a href="/web/login" class="btn border-none p-1 lh-1  text-reset o_not_editable"
                   t-if="not is_connected">
                    <t t-call="modula_web.user_icon"/>
                    <!--                    <img src="/modula_web/static/src/svg/user-icon.svg" alt="User"-->
                    <!--                         width="25" height="25"/>-->
                </a>
            </li>

            <!--Cart-->
            <t t-call="modula_web.header_cart_link"></t>
        </ul>
    </template>

    <!-- Desktop Web Shopping Cart-->
    <template id="header_cart_link" name="Header Cart Link">
        <t t-nocache="The number of products is dynamic, this rendering cannot be cached."
           t-nocache-_icon="_icon"
           t-nocache-_text="_text"
           t-nocache-_badge="_badge"
           t-nocache-_badge_class="_badge_class"
           t-nocache-_icon_wrap_class="_icon_wrap_class"
           t-nocache-_text_class="_text_class"
           t-nocache-_item_class="_item_class"
           t-nocache-_link_class="_link_class">
            <t t-set="website_sale_cart_quantity"
               t-value="request.session['website_sale_cart_quantity'] if 'website_sale_cart_quantity' in request.session else website.sale_get_order().cart_quantity or 0"/>
            <t t-set="show_cart" t-value="website.has_ecommerce_access()"/>
            <li t-attf-class="#{_item_class} divider d-none"/> <!-- Make sure the cart and related menus are not folded (see autohideMenu) -->
            <li t-attf-class="o_wsale_my_cart nav-item #{not show_cart and 'd-none'} #{_item_class}">
                <a href="/shop/cart"
                   t-attf-class=" btn position-relative rounded-circle border-0 p-1 text-reset"
                   aria-label="eCommerce cart">
                    <div t-attf-class="#{_icon_wrap_class}">
                        <!--                        <i class="fa fa-shopping-cart fa-stack"/>-->

                        <t t-call="modula_web.shopping_cart_icon"/>

                        <sup t-attf-class="my_cart_quantity badge bg-primary position-absolute top-0 end-0 mt-n1 me-n1 rounded-pill #{'d-none' if (website_sale_cart_quantity == 0) else ''}"
                             t-esc="website_sale_cart_quantity"
                             t-att-data-order-id="request.session.get('sale_order_id', '')"/>
                    </div>
                    <span t-if="_text" t-attf-class="#{_text_class}">My Cart</span>
                </a>
            </li>
        </t>
    </template>


    <!--Mobile header-->
    <template id="template_header_mobile" name="Template Header Mobile">
        <t t-call="website.navbar">
            <t t-set="is_current_page_home" t-value="request.httprequest.path == '/'"/>
            <t t-set="_navbar_classes"
               t-value="'o_header_mobile d-block d-lg-none shadow-sm ' + ('navbar_transparent' if is_current_page_home else '')"/>
            <t t-set="_navbar_expand_class" t-valuef=""/>
            <t t-set="_navbar_name" t-valuef="Mobile"/>

            <div class="o_main_nav container d-flex align-items-center justify-content-between">

                <!--Mobile Toggle-->
                <div class="nav-item first-mob-view">
                    <ul class="o_header_mobile_buttons_wrap navbar-nav flex-row align-items-center gap-2 mb-0">

                        <li class="o_not_editable">
                            <button
                                    class="nav-link btn me-auto p-2"
                                    type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#top_menu_collapse_mobile"
                                    aria-controls="top_menu_collapse_mobile"
                                    aria-expanded="false"
                                    aria-label="Toggle navigation">
                                <span>
                                    <svg class="icon icon-hamburger icon--large" viewBox="0 0 21 20" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.76807 10.0078L17.5181 10.007" stroke="currentColor"
                                              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M3.76807 5.00781L17.5181 5.00699" stroke="currentColor"
                                              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M3.76855 15.0078L17.5186 15.007" stroke="currentColor"
                                              stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </span>
                            </button>
                        </li>
                    </ul>
                </div>

                <!--Mobile Brand Logo-->
                <div class="second-mob-view text-center">
                    <a data-name="Navbar Logo" href="/" class="navbar-brand logo">

                        <span role="img" aria-label="Logo of My Website" title="My Website" data-oe-model="website"
                              data-oe-id="1" data-oe-field="logo"
                              data-oe-type="image" data-oe-expression="website.logo">
                            <t t-if="is_current_page_home">
                                <img id="logo_mob" src="/modula_web/static/src/img/website_logo_white.webp"
                                     class="img img-fluid"
                                     alt="My Website" loading="lazy"
                                     style="animation-delay: 0s; animation-duration: 0.5s;"/>
                            </t>
                            <t t-else="">
                                <img id="logo_mob" src="/modula_web/static/src/img/website_logo_black.webp"
                                     class="img img-fluid"
                                     alt="My Website" loading="lazy"
                                     style="animation-delay: 0s; animation-duration: 0.5s;"/>
                            </t>
                        </span>
                    </a>
                    <!--                    </div>-->
                </div>

                <!--Mobile Wishlist & Cart-->
                <div class="third-mob-view">

                    <t t-if="'product.wishlist' in env">
                        <t t-call="modula_web.custom_header_wishlist_link">
                            <t t-set="_icon" t-value="True"/>
                            <t t-set="_link_class"
                               t-value="' btn position-relative rounded-circle border-0 p-1 text-reset'"/>
                            <t t-set="_badge_class" t-value="'position-absolute top-0 end-0 mt-n1 me-n1'"/>
                        </t>
                    </t>

                    <t t-nocache="The number of products is dynamic, this rendering cannot be cached."
                       t-nocache-_icon="_icon"
                       t-nocache-_text="_text"
                       t-nocache-_badge="_badge"
                       t-nocache-_badge_class="_badge_class"
                       t-nocache-_icon_wrap_class="_icon_wrap_class"
                       t-nocache-_text_class="_text_class"
                       t-nocache-_item_class="_item_class"
                       t-nocache-_link_class="_link_class">
                        <t t-set="website_sale_cart_quantity"
                           t-value="request.session['website_sale_cart_quantity'] if 'website_sale_cart_quantity' in request.session else website.sale_get_order().cart_quantity or 0"/>
                        <t t-set="show_cart" t-value="website.has_ecommerce_access()"/>
                        <t t-set="_icon" t-value="True"/>
                        <!--                    <li t-attf-class="#{_item_class} divider d-none"/> -->
                        <span t-attf-class="o_wsale_my_cart nav-item #{not show_cart and 'd-none'} #{_item_class}">
                            <a href="/shop/cart"
                               t-attf-class="o_navlink_background_hover btn position-relative rounded-circle border-0 p-1 text-reset"
                               aria-label="eCommerce cart">
                                <div t-attf-class="#{_icon_wrap_class}">
                                    <!--                                    <i t-if="_icon" class="fa fa-shopping-cart fa-stack"/>-->
                                    <t t-call="modula_web.shopping_cart_icon"/>

                                    <sup t-attf-class="my_cart_quantity badge bg-primary position-absolute top-0 end-0 mt-n1 me-n1 rounded-pill #{'d-none' if (website_sale_cart_quantity == 0) else ''}"
                                         t-esc="website_sale_cart_quantity"
                                         t-att-data-order-id="request.session.get('sale_order_id', '')"/>
                                </div>
                                <span t-if="_text" t-attf-class="#{_text_class}">My Cart</span>
                            </a>
                        </span>
                    </t>
                </div>

            </div>

            <!--Mobile Sidebar-->
            <div t-attf-class="offcanvas #{_side if _side else 'offcanvas-end'} o_navbar_mobile"
                 id="top_menu_collapse_mobile">
                <div class="offcanvas-header justify-content-end o_not_editable">
                    <button type="button" class="nav-link btn-close" data-bs-dismiss="offcanvas"
                            aria-label="Close"/>
                </div>
                <div class="offcanvas-body d-flex flex-column justify-content-between h-100 w-100">
                    <ul class="navbar-nav">
                        <!-- Search bar -->
                        <t t-call="website.placeholder_header_search_box"
                           t-nocache="The searchbox should not cache previous searches.">
                            <t t-set="_classes" t-valuef="mb-4"/>
                            <t t-set="_input_classes" t-valuef="rounded-start-pill text-bg-light ps-3"/>
                            <t t-set="_submit_classes" t-valuef="rounded-end-pill bg-o-color-3 pe-3"/>
                            <t t-set="limit" t-valuef="0"/>
                        </t>
                        <!-- Navbar -->
                        <t t-call="website.navbar_nav">
                            <t t-set="_no_autohide_menu_mobile" t-valuef="True"/>
                            <t t-set="is_mobile" t-value="True"/>

                            <!-- Menu -->
                            <t t-foreach="website.menu_id.child_id" t-as="submenu">
                                <t t-call="website.submenu">
                                    <t t-set="item_class" t-valuef="nav-item mob-m-tb"/>
                                    <t t-set="link_class" t-valuef="mobile-nav-link"/>
                                    <t t-set="dropdown_toggler_classes"
                                       t-valuef="d-flex justify-content-between align-items-center"/>
                                    <t t-set="dropdown_menu_classes"
                                       t-valuef="position-relative rounded-0 o_dropdown_without_offset border-none o_animate o_anim_zoom_in o_anim_zoom_out"/>
                                </t>
                            </t>
                        </t>
                    </ul>
                    <ul class="navbar-nav gap-2 mt-3 w-100">
                        <!-- Language Selector -->
                        <t t-call="website.placeholder_header_language_selector">
                            <t t-set="_btn_class"
                               t-valuef="{{_additional_btn_color or 'nav-link'}} d-flex align-items-center w-100"/>
                            <t t-set="_txt_class" t-valuef="me-auto small"/>
                            <t t-set="_flag_class" t-valuef="me-2"/>
                            <t t-set="_div_classes" t-valuef="dropup"/>
                            <t t-set="_dropdown_menu_class" t-valuef="w-100"/>
                        </t>
                        <!-- Sign In -->
                        <t t-call="portal.placeholder_user_sign_in">
                            <t t-set="_link_class"
                               t-valuef="{{_additional_btn_color or 'nav-link o_nav_link_btn'}} w-100 border text-center"/>
                        </t>
                        <!-- User Dropdown -->
                        <t t-call="portal.user_dropdown">
                            <t t-set="_icon" t-value="true"/>
                            <t t-set="_user_name" t-value="true"/>
                            <t t-set="_user_name_class" t-valuef="me-auto small"/>
                            <t t-set="_link_class"
                               t-valuef="{{ _additional_btn_color or 'nav-link'}} d-flex align-items-center border-0"/>
                            <t t-set="_icon_class" t-valuef="me-2"/>
                            <t t-set="_item_class" t-valuef="dropdown dropup"/>
                            <t t-set="_dropdown_menu_class" t-valuef="w-100"/>
                        </t>
                        <!-- Call To Action -->
                        <!--                        <t t-call="website.header_call_to_action_large"/>-->
                    </ul>
                </div>
            </div>

        </t>
    </template>


    <!--Wishlist Link-->
    <template id="custom_header_wishlist_link" name="Custom Header Wishlit Link">

        <t t-nocache="The wishlist may vary and depends on the user."
           t-nocache-_icon="_icon"
           t-nocache-_item_class="_item_class"
           t-nocache-_link_class="_link_class"
           t-nocache-_badge_class="_badge_class"
           t-nocache-_icon_wrap_class="_icon_wrap_class"
           t-nocache-_text_class="_text_class"
           t-nocache-_text="_text">
            <t t-set="wishcount" t-value="len(request.env['product.wishlist'].current())"/>
            <t t-set="show_wishes" t-value="website.has_ecommerce_access()"/>
            <span t-attf-class="o_wsale_my_wish nav-item  #{not show_wishes and 'd-none'} #{_item_class}">
                <a href="/shop/wishlist" t-attf-class="#{_link_class}">
                    <div t-attf-class="#{_icon_wrap_class}">
                        <!--                        <i t-if="_icon" class="fa fa-1x fa-heart fa-stack"/>-->

                        <t t-call="modula_web.heart_icon"/>

                        <sup t-esc="wishcount"
                             t-attf-class="my_wish_quantity o_animate_blink badge bg-primary #{_badge_class}"/>
                    </div>
                    <span t-if="_text" t-attf-class="#{_text_class}">Wishlist</span>
                </a>
            </span>
        </t>

    </template>

    <template id="header_hide_empty_wishlist_link"
              inherit_id="modula_web.custom_header_wishlist_link">
        <xpath expr="//t[@t-set='show_wishes']" position="after">
            <t t-set="show_wishes" t-value="show_wishes and wishcount > 0"/>
        </xpath>
        <xpath expr="//span[contains(@t-attf-class, 'o_wsale_my_wish')]" position="attributes">
            <attribute name="t-attf-class" add="o_wsale_my_wish_hide_empty" separator=" "/>
        </xpath>
    </template>

    <template id="placeholder_header_search_box" name="Placeholder Header Search Bar">
        <li t-attf-class="#{_item_class} nav-item">
            <t t-if="_layout == 'modal'">
                <div class="modal fade css_editable_mode_hidden" id="o_search_modal" aria-hidden="true" tabindex="-1">
                    <div class="modal-dialog modal-lg pt-5">
                        <div class="modal-content mt-5">
                            <t t-call="website.header_search_box_input">
                                <t t-set="_classes" t-valuef="input-group-lg"/>
                            </t>
                        </div>
                    </div>
                </div>
                <a t-attf-class=" rounded-circle p-1 lh-1 #{_button_classes or 'bg-o-color-3'} o_not_editable"
                   data-bs-target="#o_search_modal" data-bs-toggle="modal" role="button" title="Search" href="#">
                    <!--                    <i class="oi oi-search fa-stack lh-lg te"/>-->
                    <t t-call="modula_web.search_icon"/>
                </a>
            </t>
            <t t-else="">
                <t t-call="website.header_search_box_input"/>
            </t>
        </li>
    </template>

    <!-- Mega Menu-->
    <template id="mega_dropdown" name="Custom Mega Menu">
        <li t-attf-class="mega-nav-item #{item_class or ''} dropdown #{submenu.is_mega_menu and 'position-static'} position-static has-submenu"
            role="presentation">
            <a t-attf-class="#{link_class or ''} dropdown-toggle o_mega_menu_toggle #{submenu.is_mega_menu and 'o_mega_menu_toggle'} #{submenu._is_active() and 'active'} #{dropdown_toggler_classes}"
               href="javascript:void(0)" onclick="return false;"
               t-att-data-bs-display="'static' if submenu.is_mega_menu else None"
               role="menuitem">
                <span class="hover-underline" t-field="submenu.name"/>
            </a>
            <!-- Mega menu-->
            <t t-if="submenu.is_mega_menu">

                <div t-if="not is_mobile"
                     t-attf-class="dropdown-menu o_mega_menu #{submenu.mega_menu_classes}"
                     data-name="Mega Menu"
                     t-field="submenu.mega_menu_content"
                     role="menuitem"/>
            </t>

            <!--Custom Mega Menu-->
            <t t-else="">
                <div data-name="Mega Menu" role="menuitem"
                     class="dropdown-menu o_mega_menu o_animate o_anim_slide_in"
                     data-oe-model="website.menu" data-oe-id="22"
                     data-oe-field="mega_menu_content" data-oe-type="html"
                     data-oe-expression="submenu.mega_menu_content" style="margin-top: 0px !important; top: unset;">
                    <section class="s_mega_menu_odoo_menu pt16 o_colored_level o_cc o_cc1 ">
                        <div class="container ">
                            <div class="row pb-1">
                                <t t-set="submenu_part_1" t-value="submenu.child_id"/>
                                <t t-set="submenu_part_2" t-value="submenu.child_id"/>
                                <!--                                    <t t-set="column_size" t-value="6"/>-->

                                <!-- Initialize a counter for submenu items -->
                                <t t-set="submenu_count" t-value="0"/>

                                <!-- Iterate over submenu.child_id to count items -->
                                <t t-foreach="submenu.child_id" t-as="child">
                                    <t t-set="submenu_count" t-value="submenu_count + 1"/>
                                </t>

                                <t t-set="column_size" t-value="(submenu_count + 1) // 2"/>

                                <h5 t-esc="submenu.name"></h5>

                                <!-- First Column -->
                                <div class="col-lg-3">
                                    <nav class="nav flex-column w-100">
                                        <t t-foreach="submenu_part_1" t-as="child">
                                            <t t-if="child_index &lt; column_size">
                                                <!--                                                    <a t-att-href="submenu._clean_url()"-->
                                                <!--                                                       t-attf-class="mb-2d {{ 'mt-3' if submenu_index == 0 else 'mt-2d' }}">-->
                                                <!--                                                        <span t-esc="submenu.name"/>-->
                                                <!--                                                    </a>-->
                                                <a t-att-href="child._clean_url()"
                                                   t-attf-class="dropdown-nav-link mb-2d mt-2d">
                                                    <span t-esc="child.name"/>
                                                </a>
                                            </t>
                                        </t>
                                    </nav>
                                </div>

                                <!-- Second Column -->
                                <div class="col-lg-3 " style="border-left: 1px solid #e2e2e2">
                                    <nav class="nav flex-column w-100">
                                        <t t-foreach="submenu_part_2" t-as="child">
                                            <t t-if="child_index &gt;= column_size">
                                                <a t-att-href="child._clean_url()"
                                                   class="dropdown-nav-link mb-2d mt-2d">
                                                    <span t-esc="child.name"/>
                                                </a>
                                            </t>
                                        </t>
                                    </nav>
                                </div>

                                <!--Third Column-->
                                <div class="col-lg-6">
                                    <div class="scroll-container">
                                        <div class="row flex-nowrap d-flex align-items-stretch">

                                            <div data-name="Card" class="col-lg-4 pb16">
                                                <div class="s_card o_card_img_top card h-100 o_cc o_cc1 my-0 o_colored_level br-0"
                                                     data-vxml="001" data-snippet="s_card" data-name="Card">
                                                    <figure class="o_card_img_wrapper ratio ratio-16x9 mb-0">
                                                        <img class="o_card_img card-img-top"
                                                             src="/web/image/website.s_three_columns_default_image_1"
                                                             alt="" loading="lazy"
                                                             data-mimetype="image/jpeg"
                                                             data-original-id="606"
                                                             data-original-src="/web/image/website.library_image_11"
                                                             data-mimetype-before-conversion="image/jpeg"/>
                                                    </figure>
                                                    <div class="card-body">
                                                        <h5 class="card-title o_default_snippet_text">
                                                            <span t-esc="submenu.name"/>
                                                            One
                                                        </h5>
                                                        <p class="card-text o_default_snippet_text">First Feature
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div data-name="Card" class="col-lg-4 pb16">
                                                <div class="s_card o_card_img_top card h-100 o_cc o_cc1 my-0 o_colored_level br-0"
                                                     data-vxml="001" data-snippet="s_card" data-name="Card">
                                                    <figure class="o_card_img_wrapper ratio ratio-16x9 mb-0">
                                                        <img class="o_card_img card-img-top"
                                                             src="/web/image/website.s_three_columns_default_image_2"
                                                             alt="" loading="lazy"
                                                             data-mimetype="image/jpeg"
                                                             data-original-id="607"
                                                             data-original-src="/web/image/website.library_image_13"
                                                             data-mimetype-before-conversion="image/jpeg"/>
                                                    </figure>
                                                    <div class="card-body">
                                                        <h5 class="card-title o_default_snippet_text">
                                                            <span t-esc="submenu.name"/>
                                                            Two
                                                        </h5>
                                                        <p class="card-text o_default_snippet_text">Second Feature
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div data-name="Card" class="col-lg-4 pb16">
                                                <div class="s_card o_card_img_top card h-100 o_cc o_cc1 my-0 o_colored_level br-0"
                                                     data-vxml="001" data-snippet="s_card" data-name="Card">
                                                    <figure class="o_card_img_wrapper ratio ratio-16x9 mb-0">
                                                        <img class="o_card_img card-img-top"
                                                             src="/web/image/website.s_three_columns_default_image_3"
                                                             alt="" loading="lazy"
                                                             data-mimetype="image/jpeg"
                                                             data-original-id="608"
                                                             data-original-src="/web/image/website.library_image_07"
                                                             data-mimetype-before-conversion="image/jpeg"/>
                                                    </figure>
                                                    <div class="card-body">
                                                        <h5 class="card-title o_default_snippet_text">
                                                            <span t-esc="submenu.name"/>
                                                            Three
                                                        </h5>
                                                        <p class="card-text o_default_snippet_text">Third Feature
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div data-name="Card" class="col-lg-4 pb16">
                                                <div class="s_card o_card_img_top card h-100 o_cc o_cc1 my-0 o_colored_level br-0"
                                                     data-vxml="001" data-snippet="s_card" data-name="Card">
                                                    <figure class="o_card_img_wrapper ratio ratio-16x9 mb-0">
                                                        <img class="o_card_img card-img-top"
                                                             src="/web/image/website.s_three_columns_default_image_1"
                                                             alt="" loading="lazy"
                                                             data-mimetype="image/jpeg"
                                                             data-original-id="606"
                                                             data-original-src="/web/image/website.library_image_11"
                                                             data-mimetype-before-conversion="image/jpeg"/>
                                                    </figure>
                                                    <div class="card-body">
                                                        <h5 class="card-title o_default_snippet_text">
                                                            <span t-esc="submenu.name"/>
                                                            One
                                                        </h5>
                                                        <p class="card-text o_default_snippet_text">First Feature
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div data-name="Card" class="col-lg-4 pb16">
                                                <div class="s_card o_card_img_top card h-100 o_cc o_cc1 my-0 o_colored_level br-0"
                                                     data-vxml="001" data-snippet="s_card" data-name="Card">
                                                    <figure class="o_card_img_wrapper ratio ratio-16x9 mb-0">
                                                        <img class="o_card_img card-img-top"
                                                             src="/web/image/website.s_three_columns_default_image_2"
                                                             alt="" loading="lazy"
                                                             data-mimetype="image/jpeg"
                                                             data-original-id="607"
                                                             data-original-src="/web/image/website.library_image_13"
                                                             data-mimetype-before-conversion="image/jpeg"/>
                                                    </figure>
                                                    <div class="card-body">
                                                        <h5 class="card-title o_default_snippet_text">
                                                            <span t-esc="submenu.name"/>
                                                            Two
                                                        </h5>
                                                        <p class="card-text o_default_snippet_text">Second Feature
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div data-name="Card" class="col-lg-4 pb16">
                                                <div class="s_card o_card_img_top card h-100 o_cc o_cc1 my-0 o_colored_level br-0"
                                                     data-vxml="001" data-snippet="s_card" data-name="Card">
                                                    <figure class="o_card_img_wrapper ratio ratio-16x9 mb-0">
                                                        <img class="o_card_img card-img-top"
                                                             src="/web/image/website.s_three_columns_default_image_3"
                                                             alt="" loading="lazy"
                                                             data-mimetype="image/jpeg"
                                                             data-original-id="608"
                                                             data-original-src="/web/image/website.library_image_07"
                                                             data-mimetype-before-conversion="image/jpeg"/>
                                                    </figure>
                                                    <div class="card-body">
                                                        <h5 class="card-title o_default_snippet_text">
                                                            <span t-esc="submenu.name"/>
                                                            Three
                                                        </h5>
                                                        <p class="card-text o_default_snippet_text">Third Feature
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </section>
                </div>
            </t>
        </li>
    </template>


    <!--simple dropdown menu-->
    <template id="simple_dropdown" name="Simple Dropdown">
        <li t-attf-class="mega-nav-item #{item_class or ''} dropdown #{submenu.is_mega_menu and 'position-static'} simple_dropdown has-submenu hover-underline"
            role="presentation">
            <!--            <a t-attf-class="#{link_class or ''} dropdown-toggle #{submenu.is_mega_menu and 'o_mega_menu_toggle'} #{submenu._is_active() and 'active'} #{dropdown_toggler_classes}"-->
            <!--               data-bs-toggle="dropdown" href="#" t-att-data-bs-display="'static' if submenu.is_mega_menu else None"-->
            <!--               role="menuitem">-->
            <!--                <span t-field="submenu.name"/>-->
            <!--            </a>-->

            <a t-attf-class="#{link_class or ''} dropdown-toggle #{submenu.is_mega_menu and 'o_mega_menu_toggle'} #{submenu._is_active() and 'active'} #{dropdown_toggler_classes}"
               href="javascript:void(0)"
               onclick="return false;"
               t-att-data-bs-display="'static' if submenu.is_mega_menu else None"
               role="menuitem">
                <span class="hover-dropdown-underline-text" t-field="submenu.name"/>
            </a>

            <t t-if="submenu.is_mega_menu">
                <div t-if="not is_mobile"
                     t-attf-class="dropdown-menu o_mega_menu #{submenu.mega_menu_classes}"
                     data-name="Mega Menu"
                     t-field="submenu.mega_menu_content"
                     role="menuitem"/>
            </t>

            <t t-else="">
                <ul t-attf-class="dropdown-menu-s dropdown-menu #{dropdown_menu_classes} o_animate o_anim_fade_in o_anim_from_top"
                    role="menu"
                    style="--wanim-intensity: 1;
                           animation-delay: 0.2s;
                           animation-duration: 0.8s;">
                    <t t-foreach="submenu.child_id" t-as="submenu">
                        <t t-call="modula_web.submenu">
                            <t t-set="item_class" t-value="None"/>
                            <!--                            <t t-set="link_class" t-valuef="dropdown-item"/>-->
                            <t t-set="link_class" t-value="'dropdown-item inactive'"/>
                        </t>
                    </t>
                </ul>
            </t>
        </li>
    </template>

    <!--Custom submenu-->
    <template id="submenu" name="Submenu">
        <t t-set="show_dropdown"
           t-value="(submenu.is_mega_menu and submenu.is_visible) or submenu.child_id.filtered(lambda menu: menu.is_visible)"/>
        <li t-if="submenu.is_visible and not (submenu.child_id or submenu.is_mega_menu)"
            t-attf-class="#{item_class or ''}" role="presentation">
            <a t-att-href="submenu._clean_url()"
               t-attf-class="#{link_class or ''}"
               role="menuitem"
               t-ignore="true"
               t-att-target="'_blank' if submenu.new_window else None">
                <span t-field="submenu.name"/>
            </a>
        </li>
        <li t-elif="show_dropdown"
            t-attf-class="#{item_class or ''} dropdown #{submenu.is_mega_menu and 'position-static'}"
            role="presentation">
            <a t-attf-class="#{link_class or ''} dropdown-toggle #{submenu.is_mega_menu and 'o_mega_menu_toggle'} #{dropdown_toggler_classes}"
               data-bs-toggle="dropdown" href="#" t-att-data-bs-display="'static' if submenu.is_mega_menu else None"
               role="menuitem">
                <span t-field="submenu.name"/>
            </a>
            <t t-if="submenu.is_mega_menu">
                <div t-if="not is_mobile"
                     t-attf-class="dropdown-menu o_mega_menu #{submenu.mega_menu_classes}"
                     data-name="Mega Menu"
                     t-field="submenu.mega_menu_content"
                     role="menuitem"/>
            </t>
            <ul t-else="" t-attf-class="dropdown-menu #{dropdown_menu_classes}" role="menu">
                <t t-foreach="submenu.child_id" t-as="submenu">
                    <t t-call="modula_web.submenu">
                        <t t-set="item_class" t-value="None"/>
                        <t t-set="link_class" t-valuef="dropdown-item"/>
                    </t>
                </t>
            </ul>
        </li>
    </template>

</odoo>