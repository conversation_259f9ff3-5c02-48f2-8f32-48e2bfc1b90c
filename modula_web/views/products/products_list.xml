<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="products_extended" inherit_id="modula_product.products" name="Custom Product Lists Page">

        <xpath expr="//aside[@id='products_grid_before']" position="replace">
            <aside t-if="False" id="products_grid_before"
                   class="d-none d-lg-block position-sticky col-3 px-3 clearfix">
                <div class="o_wsale_products_grid_before_rail vh-100 ms-n2 mt-n2 pt-2 p-lg-2 pb-lg-5 ps-2 overflow-y-scroll">
                    <t t-set="is_sidebar_collapsible"
                       t-value="is_view_active('website_sale.products_categories_list_collapsible')"/>
                    <div t-if="opt_wsale_categories"
                         t-att-class="'products_categories'
                                                + (' accordion accordion-flush' if is_sidebar_collapsible else ' mb-3')">
                        <t t-call="website_sale.products_categories_list"/>
                    </div>
                    <div t-attf-class="products_attributes_filters d-empty-none {{opt_wsale_categories and 'border-top'}}"/>
                    <t t-if="opt_wsale_filter_price and opt_wsale_attributes"
                       t-call="website_sale.filter_products_price">
                        <t t-set="_classes"
                           t-valuef="{{is_sidebar_collapsible and 'accordion accordion-flush'}} {{is_sidebar_collapsible and (opt_wsale_categories or len(attributes) > 0) and 'border-top'}}"/>
                    </t>
                </div>
            </aside>
        </xpath>

        <xpath expr="//div[@id='products_grid']" position="replace">

            <div class="rows">

                <!--Bread Crumb-->
                <div class="col-md-12 breadcrumb nopadding">
                    <a href="/">Home</a>
                    <span>
                        <i class="fa fa-angle-right" aria-hidden="true"></i>
                    </span>
                    <!--                    <span>Shop all</span>-->
                    <!--                    <span t-esc="category.name.split(' ')[-1]"/>-->

                    <t t-if="category.parents_and_self">
                        <span t-esc="category.parents_and_self[0].name"></span>
                    </t>
                    <t t-else="">
                        <span>Shop all</span>
                    </t>
                </div>

                <div class="col-md-12 text-center mt-4" t-if="category">
                    <h1 style="letter-spacing: 2px" t-esc="category.name"/>

                    <!--                    <t t-if="'Sofas' in category.parents_and_self[0].name">-->
                    <!--                        <h1 style="letter-spacing: 2px" t-esc="category"/>-->
                    <!--                        <h5 style="letter-spacing: 2px">Sofas &amp; Couches</h5>-->
                    <!--                    </t>-->
                    <!--                    <t t-else="">-->
                    <!--                        <h5 t-esc="category.parents_and_self[0].name"/>-->
                    <!--                    </t>-->

                </div>

                <t t-if="category">
                    <div class="mb16 fw-300 mt-3 mb-3 text-center" id="category_header"
                         t-att-data-editor-message="editor_msg"
                         t-field="category.website_description"/>
                </t>

                <!--Other Related Categories-->
                <t t-if="opt_wsale_categories_top"
                   t-call="modula_web.filmstrip_categories"/>

                <!--Sort By & Grid-->
                <div class="products_header text-end justify-content-between gap-3 mb-3">
                    <!--                    <t t-if="is_view_active('website_sale.search')" t-call="website_sale.search">-->
                    <!--                        <t t-set="search" t-value="original_search or search"/>-->
                    <!--                        <t t-set="_form_classes" t-valuef="d-lg-inline {{'d-inline' if not category else 'd-none'}}"/>-->
                    <!--                    </t>-->

                    <!--                    <t t-call="website_sale.pricelist_list">-->
                    <!--                        <t t-set="_classes" t-valuef="d-none d-lg-inline"/>-->
                    <!--                    </t>-->

                    <!--                    <t t-if="is_view_active('website_sale.sort')" t-call="modula_web.sort_by">-->
                    <!--                        <t t-set="_classes" t-valuef="d-none me-auto d-lg-inline-block"/>-->
                    <!--                    </t>-->

                    <t t-call="modula_web.sort_by">
                        <t t-set="_classes" t-valuef="me-auto d-lg-inline-block"/>
                    </t>

                    <!--                    <div t-if="category" class="d-flex align-items-center d-lg-none me-auto">-->
                    <!--                        <t t-if="not category.parent_id" t-set="backUrl" t-valuef="/shop"/>-->
                    <!--                        <t t-else="" t-set="backUrl"-->
                    <!--                           t-value="keep('/shop/category/' + slug(category.parent_id), category=0)"/>-->

                    <!--                        <a t-attf-class="btn btn-{{navClass}} me-2"-->
                    <!--                           t-att-href="category.parent_id and keep('/shop/category/' + slug(category.parent_id), category=0) or '/shop'">-->
                    <!--                            <i class="fa fa-angle-left"/>-->
                    <!--                        </a>-->
                    <!--                        <h4 t-out="category.name" class="mb-0 me-auto"/>-->
                    <!--                    </div>-->

                    <!--                    <t t-if="is_view_active('website_sale.add_grid_or_list_option')"-->
                    <!--                       t-call="website_sale.add_grid_or_list_option">-->
                    <!--                        <t t-set="_classes" t-valuef="d-flex"/>-->
                    <!--                    </t>-->

                    <!--                    <button t-if="is_view_active('website_sale.sort') or opt_wsale_categories or opt_wsale_attributes or opt_wsale_attributes_top"-->
                    <!--                            t-attf-class="btn btn-{{navClass}} position-relative {{not opt_wsale_attributes_top and 'd-lg-none'}}"-->
                    <!--                            data-bs-toggle="offcanvas"-->
                    <!--                            data-bs-target="#o_wsale_offcanvas">-->
                    <!--                        <i class="fa fa-sliders"/>-->
                    <!--                        <span t-if="isFilteringByPrice or attrib_set or tags"-->
                    <!--                              t-attf-class="position-absolute top-0 start-100 translate-middle border border-{{navClass}} rounded-circle bg-danger p-1">-->
                    <!--                            <span class="visually-hidden">filters active</span>-->
                    <!--                        </span>-->
                    <!--                    </button>-->
                </div>


                <div t-if="original_search and products" class="alert alert-warning mt8">
                    No results found for '<span t-esc="original_search"/>'. Showing results for '
                    <span t-esc="search"/>
                    '.
                </div>

                <!-- If categories has products-->
                <div t-if="products" class="o_wsale_products_grid_table_wrapper pt-3 pt-lg-0">
                    <t t-set="grid_md_allow_custom_cols" t-value="hasLeftColumn"/>
                    <t t-set="grid_md_use_3col"
                       t-value="not hasLeftColumn and ppr == 4"/>

                    <section
                            id="o_wsale_products_grid"
                            t-attf-class="o_wsale_products_grid_table grid {{grid_md_allow_custom_cols and 'o_wsale_products_grid_table_md'}}"
                            t-attf-style="--o-wsale-products-grid-gap: {{gap}}; --o-wsale-ppr: {{ppr}}; --o-wsale-ppg: {{ppg}}"
                            t-att-data-ppg="ppg"
                            t-att-data-ppr="ppr"
                            t-att-data-default-sort="website.shop_default_sort"
                            t-att-data-name="grid_block_name"
                    >
                        <t t-foreach="bins" t-as="tr_product">
                            <t t-foreach="tr_product" t-as="td_product">
                                <t t-if="td_product">
                                    <t t-set="col_height" t-value="td_product['y']"/>
                                    <t t-set="col_width"
                                       t-value="12 // ppr * td_product['x']"/>
                                    <t t-set="col_class_lg"
                                       t-value="'g-col-lg-' + str(col_width)"/>
                                    <t t-set="col_class_md"
                                       t-value="grid_md_allow_custom_cols and ('g-col-md-' + str(col_width)) or grid_md_use_3col and 'g-col-md-4' or 'g-col-md-6'"/>
                                    <t t-set="col_is_stretched"
                                       t-value="(td_product['x'] &gt;= td_product['y'] * 2)"/>
                                    <t t-set="col_is_custom_portrait"
                                       t-value="not col_is_stretched and (col_height &gt; td_product['x'])"/>
                                    <div
                                            t-attf-class="oe_product {{col_is_custom_portrait and 'oe_product_custom_portrait'}} g-col-6 {{col_class_md}} {{col_class_lg}} {{col_is_stretched and 'oe_product_size_stretch'}}"
                                            t-attf-style="--o-wsale-products-grid-product-col-height: {{col_height}};"
                                            t-att-data-ribbon-id="td_product['ribbon'].id"
                                            t-att-data-colspan="td_product['x'] != 1 and td_product['x']"
                                            t-att-data-rowspan="td_product['y'] != 1 and td_product['y']"
                                            t-att-data-name="product_block_name"
                                    >
                                        <div t-attf-class="o_wsale_product_grid_wrapper position-relative h-100 o_wsale_product_grid_wrapper_#{td_product['x']}_#{td_product['y']}">
                                            <t t-call="modula_web.products_item">
                                                <t t-set="product"
                                                   t-value="td_product['product']"/>
                                            </t>
                                        </div>
                                    </div>
                                </t>
                            </t>
                        </t>
                    </section>

                    <!--Pagination-->
                    <div class="products_pager d-flex justify-content-center pt-5 pb-3">
                        <t t-call="modula_web.pagination"/>
                    </div>
                </div>

                <!-- If categories has no products-->
                <div t-else="" class="text-center text-muted no-product-border">

                    <t t-if="not search">
                        <h3 class="mt8">No product defined in this category.</h3>
                        <!--                        <p t-if="category">No product defined in this category.</p>-->
                    </t>
                    <t t-else="">
                        <h3 class="mt8">No results</h3>
                        <p>No results for "<strong t-esc='search'/>"
                            <t t-if="category">in category "<strong t-esc="category.display_name"/>"
                            </t>
                            .
                        </p>
                    </t>
                    <!--                    <p t-ignore="true" groups="sales_team.group_sale_manager">Click <i>'New'</i> in the top-right corner-->
                    <!--                        to create your first product.-->
                    <!--                    </p>-->
                </div>


            </div>
        </xpath>

    </template>


    <!--Other Categories-->
    <template id="filmstrip_categories" name="Categories Filmstrip">

        <t t-if="category.id">
            <t t-set="entries"
               t-value="not search and category.child_id or category.child_id.filtered(lambda c: category.id in search_categories_ids)"/>

            <t t-if="not entries">
                <t t-set="parent" t-value="category.parent_id"/>
                <t t-set="entries"
                   t-value="not search and parent.child_id or parent.child_id.filtered(lambda c: parent.id in search_categories_ids)"/>
            </t>
        </t>
        <t t-else="">
            <t t-set="entries" t-value="categories"/>
        </t>

        <!--Sort by sequence-->
        <t t-set="entries" t-value="sorted(entries, key=lambda c: c.sequence)"/>

        <div t-if="entries" class="o_wsale_filmstip_container d-flex align-items-stretch overflow-hidden">
            <div class="o_wsale_filmstip_wrapper pb-1 overflow-auto">
                <ul class="o_wsale_filmstip d-flex align-items-stretch mb-0 list-unstyled overflow-visible mb-1">
                    <t t-if="category.parent_id" t-set="backUrl"
                       t-value="keep('/shop/category/' + slug(category.parent_id), category=0)"/>
                    <t t-else="" t-set="backUrl" t-value="'/shop'"/>

                    <li t-foreach="entries" t-as="c" t-attf-class="d-flex {{'pe-3' if not c_last else ''}}">
                        <a t-att-href="keep('/shop/category/' + slug(c), category=0)" class="text-decoration-none"
                           draggable="false">
                            <input type="radio"
                                   t-attf-name="wsale_categories_top_radios_{{parentCategoryId}}"
                                   class="btn-check pe-none"
                                   t-att-id="c.id"
                                   t-att-value="c.id"
                                   t-att-checked="'true' if c.id == category.id else None"/>
                            <div t-att-class="'o_cc1 d-flex align-items-center chips h-100 btn btn-'
                                    + navClass
                                    + (c.image_128 and ' ps-2 pe-3' or ' px-4')
                                    + (c.id == category.id and ' border-primary' or '')">
                                <div t-if="c.image_128"
                                     t-attf-style="background-image:url('data:image/png;base64,
                                        #{c.image_128}')"
                                     class="o_image_40_cover oe_img_bg o_bg_img_center
                                        flex-shrink-0 rounded-3 me-3"
                                     t-att-alt="c.name "/>
                                <span class="text-nowrap" t-field="c.name"/>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </template>

    <!--Sort By Dropdown-->
    <template id="sort_by" name="Custom Sort-by Template">
        <div t-attf-class="o_sortby_dropdown dropdown dropdown_sorty_by {{_classes}}">
            <a role="button" href="#" t-attf-class="dropdown-toggle btn border-none nopadding"
               data-bs-toggle="dropdown">
                <small class="d-lg-inline text-muted fw-600">Sort by:</small>
                <span class="d-lg-inline fw-300 font-small">
                    <t t-if="isSortingBy" t-out="isSortingBy[0][1]"/>
                    <span t-else="1" t-field="website.shop_default_sort"/>
                </span>
                <!--                <i class="fa fa-sort-amount-asc d-lg-none"/>-->
                <!--                <i class="fa fa-angle-down" aria-hidden="true"></i>-->
            </a>
            <div class="dropdown-menu dropdown-menu-end" role="menu" t-attf-style="background: white">
                <t t-foreach="website_sale_sortable" t-as="sortby">
                    <a role="menuitem" rel="noindex,nofollow" t-att-href="keep('/shop', order=sortby[0])"
                       class="dropdown-item fw-300">
                        <span t-out="sortby[1]"/>
                    </a>
                </t>
            </div>
        </div>
    </template>

    <!--Product Items-->
    <template id="products_item" name="Custom Products item">

        <t t-set="variant_count" t-value="len(product.product_variant_ids)"/>
        <t t-set="cheapest_variant" t-value="product.get_cheapest_variant_price(category)"/>
        <t t-if="cheapest_variant and cheapest_variant['variant']">
            <t t-set="cheapest_cover_attribute" t-value="cheapest_variant['cover_attribute']"/>
            <t t-set="cheapest_colour_attribute" t-value="cheapest_variant['colour_attribute']"/>
            <t t-set="cheapest_combination" t-value="cheapest_variant['variant'].combination_indices"/>
        </t>


        <form action="/shop/cart/update" method="post" class="oe_product_cart  h-100 d-flex"
              t-att-data-publish="product.website_published and 'on' or 'off'"
              itemscope="itemscope" itemtype="http://schema.org/Product">
            <t t-set="initial_product_href"
               t-value="keep(product.website_url, page=(pager['page']['num'] if pager['page']['num']&gt;1 else None)) + selected_attributes_hash"/>

            <t t-set="product_href"
               t-value="initial_product_href + ('&amp;combination=' + cheapest_combination if cheapest_combination else '')"/>

            <t t-set="image_type" t-value="product._get_suitable_image_size(ppr, td_product['x'], td_product['y'])"/>

            <!--Product Image-->
            <div class="oe_product_image oe_product_image_link position-relative flex-grow-0 overflow-hidden">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"
                       t-nocache="The csrf token must always be up to date."/>

                <t t-set="hover_product_img_url" t-value="get_mouseover_product_image(product)"></t>

                <!--Image when not hovered-->

                <section class="product-default-image">
                    <t t-call="modula_web.product_image"/>
                </section>

                <!--Image when hovered-->

                <section class="product-hover-image">
                    <t t-if="hover_product_img_url">
                        <a t-att-href="product_href"
                           class="oe_product_image_link d-block position-relative"
                           itemprop="url"
                           contenteditable="false">

                            <span class="oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute">

                                <img t-att-src="hover_product_img_url"
                                     class="oe_product_image_img_wrapper d-flex w-100 position-absolute"
                                     itemprop="image"
                                     alt="Product Image"
                                     height="225"
                                     loading="lazy"/>

                            </span>
                        </a>
                    </t>

                    <t t-else="">
                        <t t-call="modula_web.product_image"/>
                    </t>
                </section>


                <!--                &lt;!&ndash;Image on Hover&ndash;&gt;-->
                <!--                <t t-foreach="product.image_entry_ids" t-as="extra">-->

                <!--                    <t t-if="extra.config_id and extra.config_id.name == 'mouseover'">-->

                <!--                        <t t-if="extra.image_ids">-->
                <!--                            <t t-set="image_url"-->
                <!--                               t-value="'/web/image/ir.attachment/%s/datas' % extra.image_ids[0].id"/>-->
                <!--                            <a t-att-href="product_href"-->
                <!--                               class="oe_product_image_link d-block position-relative"-->
                <!--                               itemprop="url"-->
                <!--                               contenteditable="false">-->

                <!--                                <img t-att-src="image_url"-->
                <!--                                     class="oe_product_image_img_wrapper d-flex w-100 position-absolute"-->
                <!--                                     itemprop="image"-->
                <!--                                     alt="Product Image"-->
                <!--                                     loading="lazy"/>-->
                <!--                            </a>-->
                <!--                        </t>-->


                <!--                    </t>-->
                <!--                </t>-->


                <!--Add Wishlist Icon-->
                <t t-call="modula_web.custom_add_to_wishlist" t-if="'product.wishlist' in env"></t>
            </div>

            <div class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1">
                <div class="o_wsale_product_information_text mb-1d">

                    <!--Test Product hover image-->

                    <!--                    <t t-foreach="product.image_entry_ids" t-as="extra">-->

                    <!--                        <t t-if="extra.config_id and extra.config_id.name == 'mouseover'">-->

                    <!--                            <t t-if="extra.image_ids">-->
                    <!--                                <t t-set="image_url"-->
                    <!--                                   t-value="'/web/image/ir.attachment/%s/datas' % extra.image_ids[0].id"/>-->
                    <!--                                <img t-att-src="image_url"-->
                    <!--                                     class="oe_product_image_img_wrapper d-flex h-100 w-100 position-absolute"-->
                    <!--                                     itemprop="image"-->
                    <!--                                     alt="Product Image"-->
                    <!--                                     loading="lazy"/>-->
                    <!--                            </t>-->

                    <!--                        </t>-->


                    <!--                    </t>-->

                    <!--Product Name-->

                    <!--For Range Product-->
                    <t t-if="product.is_range">
                        <t t-set="product_name"
                           t-value="product.name.rstrip().rsplit(' ', 1)[0] if product.name.lower().endswith('range') else product.name"/>
                    </t>

                    <t t-else="">
                        <t t-set="product_name"
                           t-value="product.name.replace('-', '')"/>
                    </t>

                    <p class="o_wsale_products_item_title text-break mb-0">
                        <a class="fw-300 text-decoration-none text-primary-emphasis f-18" itemprop="name"
                           t-att-href="product_href">
                            <t t-esc="product_name"/>
                        </a>
                        <a t-if="not product.website_published" role="button" t-att-href="product_href"
                           class="btn btn-sm btn-danger" title="This product is unpublished.">
                            Unpublished
                        </a>
                    </p>


                    <h6 class="text-break text-secondary f-14 mt-2d">20% Off – Grand Opening Offer</h6>


                    <!--Product Colors-->

                    <t t-set="color_attributes" t-value="get_color_attributes(product)"/>

                    <!--                    <t t-set="color_attributes_count" t-value="len(color_attributes)"/>-->
                    <t t-set="total_color_count" t-value="0"/>
                    <t t-foreach="color_attributes" t-as="attr">
                        <t t-set="total_color_count" t-value="total_color_count + attr['count']"/>
                    </t>


                    <t t-if="total_color_count &gt; 0">
                        <!--                        <p class="mt-2d mb-2d">-->
                        <small class="text-break fw-300">
                            <span t-esc="total_color_count"/>
                            colours
                        </small>
                        <!--                        </p>-->

                        <!--                        <p class="mt-2d">-->
                        <!--                            <t t-foreach="color_attributes" t-as="ptav" class="list-inline-item me-1"-->
                        <!--                               style="position: relative">-->

                        <!--                                <t t-set="img_style"-->
                        <!--                                   t-value="'background:url(/web/image/product.attribute.value/%s/image); background-size:cover; height: 20px; width:20px;' % ptav.id if ptav.image else ''"/>-->
                        <!--                                <t t-set="color_style"-->
                        <!--                                   t-value="'background:' + str(ptav.html_color or ptav.name if not ptav.is_custom else '')"/>-->

                        <!--                                <label t-attf-style="#{img_style or color_style}">-->
                        <!--                                    <input type="radio" style="visibility: hidden"></input>-->
                        <!--                                </label>-->
                        <!--                            </t>-->
                        <!--                        </p>-->

                    </t>


                </div>

                <div class="o_wsale_product_sub d-flex justify-content-between align-items-end gap-2 flex-wrap f-14">
                    <t t-set="template_price_vals" t-value="get_product_prices(product)"/>

                    <div class="product_price" itemprop="offers" itemscope="itemscope"
                         itemtype="http://schema.org/Offer">
                        <!--                        <span class="fw-400" t-if="product.is_range">From</span>-->
                        <!--                        <span class="fw-400">From</span>-->


                        <!--                        <t t-if="product.is_range">-->
                        <!--                            <span class="fw-400">From</span>-->
                        <!--                        </t>-->
                        <!--                        <t t-else="">-->
                        <!--                            <span class="fw-400" t-if="variant_count &gt; 1">From</span>-->
                        <!--                        </t>-->

                        <!--                        <span>$-->
                        <!--                            <span class="oe_currency_value" t-out="'{0:,.2f}'.format(variant_cheapest_price)"/>-->
                        <!--                        </span>-->

                        <!--                        <span class="mb-0 fw-400"-->
                        <!--                              t-if="template_price_vals['price_reduce'] or not website.prevent_zero_price_sale"-->
                        <!--                              t-out="template_price_vals['price_reduce']"-->
                        <!--                              t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>-->


                        <span t-if="template_price_vals['price_reduce'] or not website.prevent_zero_price_sale">


                            <!--                            <span>Original Price</span>-->
                            <!--                            <span class="mb-0 fw-400" t-out="template_price_vals['price_reduce']"-->
                            <!--                                  t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>-->
                            <!--                            <br/>-->

                            <!--                            <span>Lowest V Price</span>-->
                            <!--                            <t t-esc="product.min_variant_price"/>-->
                            <!--                            <br/>-->


                            <t t-if="product.is_range">
                                <span class="fw-400">From</span>
                                <span class="mb-0 fw-400" t-out="template_price_vals['price_reduce']"
                                      t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                            </t>
                            <t t-else="">
                                <span class="fw-400" t-if="variant_count &gt; 1">
                                    <span>From</span>
                                    <span t-out="'${0:,.2f}'.format(cheapest_variant.get('price'))"/>
                                </span>
                                <span t-else="">
                                    <span class="mb-0 fw-400" t-out="template_price_vals['price_reduce']"
                                          t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                                </span>

                            </t>


                        </span>


                        <span class="mb-0 fw-400"
                              t-elif="any(ptav.price_extra for ptav in product.attribute_line_ids.product_template_value_ids)">
                            &amp;nbsp;
                        </span>
                        <span class="mb-0 fw-400" t-else="" t-field="website.prevent_zero_price_sale_text"/>
                        <t t-if="'base_price' in template_price_vals and (template_price_vals['base_price'] &gt; template_price_vals['price_reduce']) and (template_price_vals['price_reduce'] or not website.prevent_zero_price_sale)">
                            <del t-attf-class="text-muted me-1 mb-0 fw-400 f-14" style="white-space: nowrap;">
                                <em class="small" t-esc="template_price_vals['base_price']"
                                    t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                            </del>
                        </t>
                        <span itemprop="price" style="display:none;" t-esc="template_price_vals['price_reduce']"/>
                        <span itemprop="priceCurrency" style="display:none;" t-esc="website.currency_id.name"/>
                    </div>
                </div>
            </div>
        </form>
    </template>

    <!--Add to wishlist-->
    <template id="custom_add_to_wishlist" name="Custom Wishlist Button">
        <div class="wishlist-icon">
            <t t-set="in_wish" t-value="product in products_in_wishlist"/>
            <t t-set="product_variant_id" t-value="in_wish or product._get_first_possible_variant_id()"/>
            <button
                    t-if="product_variant_id and product.type != 'combo'"
                    type="button"
                    role="button"
                    class="o_add_wishlist"
                    t-att-disabled="in_wish"
                    t-att-data-product-template-id="product.id"
                    t-att-data-product-product-id="product_variant_id"
                    data-action="o_wishlist"
                    title="Add to Wishlist">

                <span t-if="not in_wish">
                    <i class="fa fa-heart-o fa-lg" aria-hidden="true"></i>
                </span>

                <span t-if="in_wish">
                    <i class="fa fa-heart fa-lg" aria-hidden="true"></i>
                </span>

                <!--                <span class="fa fa-heart" role="img" aria-label="Add to wishlist"/>-->
            </button>
        </div>
    </template>


    <template id="pagination" name="Custom Pagination">

        <ul t-if="pager['page_count'] > 1"
            t-attf-class="#{ classname or '' } pagination m-0 #{_classes}"
            t-att-style="style or None">
            <li t-attf-class="page-item #{'disabled' if pager['page']['num'] == 1 else ''}">
                <a
                        t-att-href=" pager['page_previous']['url'] if pager['page']['num'] != 1 else None"
                        t-attf-class="page-link #{extraLinkClass}">
                    <span class="fa fa-chevron-left" role="img" aria-label="Previous"
                          title="Previous"/>
                </a>
            </li>
            <t t-foreach="pager['pages']" t-as="page">
                <li
                        t-attf-class="page-item #{'active' if page['num'] == pager['page']['num'] else ''}">
                    <a t-att-href="page['url']" t-attf-class="page-link #{extraLinkClass}"
                       t-out="page['num']"/>
                </li>
            </t>
            <li
                    t-attf-class="page-item #{'disabled' if pager['page']['num'] == pager['page_count'] else ''}">
                <a
                        t-att-href="pager['page_next']['url'] if pager['page']['num'] != pager['page_count'] else None"
                        t-attf-class="page-link #{extraLinkClass}">
                    <span class="fa fa-chevron-right" role="img" aria-label="Next" title="Next"/>
                </a>
            </li>
        </ul>
    </template>


    <template id="product_image" name="Product Image">
        <a t-att-href="product_href"
           class="oe_product_image_link d-block position-relative"
           itemprop="url"
           contenteditable="false">
            <t t-set="image_holder" t-value="product._get_image_holder()"/>
            <span t-field="image_holder.image_1920"
                  t-options="{'widget': 'image', 'preview_image': image_type, 'itemprop': 'image', 'class': 'h-100 w-100 position-absolute'}"
                  class="oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute"/>

            <t t-set="bg_color" t-value="td_product['ribbon'].bg_color"/>
            <t t-set="text_color" t-value="td_product['ribbon'].text_color"/>
            <t t-set="bg_class" t-value="td_product['ribbon']._get_position_class()"/>
            <span t-attf-class="o_ribbon o_not_editable #{bg_class}"
                  t-attf-style="#{text_color and ('color: %s;' % text_color)} #{bg_color and 'background-color:' + bg_color}"
                  t-out="td_product['ribbon'].name or ''"/>
        </a>
    </template>

</odoo>