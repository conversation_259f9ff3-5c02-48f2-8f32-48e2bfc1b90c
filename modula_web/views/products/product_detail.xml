<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="product_detail_extended" inherit_id="modula_product.product" name="Custom Product Detail Page">
        <xpath expr="//section[@id='product_detail']" position="replace">

            <t t-set="combination_info" t-value="new_combination_info or product._get_combination_info()"/>
            <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>

            <section id="product_detail" t-attf-class="oe_website_sale container-fluid #{'discount'
                            if combination_info['has_discounted_price'] else ''}"
                     t-att-data-view-track="view_track and '1' or '0'"
                     t-att-data-product-tracking-info="'product_tracking_info' in combination_info and json.dumps(combination_info['product_tracking_info'])">

                <!--Breadcrumbs-->
                <div class="row align-items-center">
                    <div class="col d-flex align-items-center order-1 order-lg-0 f-8">
                        <ol class="o_wsale_breadcrumb breadcrumb p-0 m-lg-0">
                            <li class="o_not_editable breadcrumb-item d-none d-lg-inline-block">
                                <a t-att-href="keep(category=0, attribute_value=0, tags=0)">
                                    <i class="oi oi-chevron-left d-lg-none me-1" role="presentation"/>All Products
                                </a>
                            </li>
                            <li t-nocache="The category does not have to be cached, as the product can be accessed via different paths."
                                t-if="category" class="breadcrumb-item">
                                <a class="py-2 py-lg-0"
                                   t-att-href="keep('/shop/category/%s' % slug(category), category=0, attribute_value=0, tags=0)">
                                    <i class="oi oi-chevron-left d-lg-none me-1" role="presentation"/>
                                    <t t-out="category.name"/>
                                </a>
                            </li>
                            <li t-else="" class="o_not_editable breadcrumb-item d-lg-none">
                                <a class="py-2 py-lg-0"
                                   t-att-href="keep(category=0, attribute_value=0, tags=0)">
                                    <i class="oi oi-chevron-left me-1" role="presentation"/>All Products
                                </a>
                            </li>
                            <li class="breadcrumb-item d-none d-lg-inline-block active">
                                <span t-field="product.name"/>
                            </li>
                        </ol>
                    </div>

                    <!--Search Bar currently disabled-->
                    <div class="col-lg-4 d-flex align-items-center d-none">
                        <div class="d-flex justify-content-between w-100 ">
                            <t t-if="is_view_active('website_sale.search')" t-call="website_sale.search">
                                <t t-set="search" t-value="False"/>
                                <t t-set="_form_classes" t-valuef="mb-4 mb-lg-0"/>
                                <t t-set="_classes" t-value="'me-sm-2'"/>
                            </t>
                            <t t-call="website_sale.pricelist_list">
                                <t t-set="_classes" t-valuef="d-lg-inline ms-2"/>
                            </t>
                        </div>
                    </div>
                </div>

                <!--Product Details-->
                <div class="row mt-5d" id="product_detail_main" data-name="Product Page"
                     t-att-data-image_width="website.product_page_image_width"
                     t-att-data-image_layout="website.product_page_image_layout">
                    <t t-set="image_cols" t-value="website._get_product_page_proportions()"/>

                    <!-- Product Image-->
                    <div t-attf-class="col-lg-#{image_cols[0]} mt-lg-2 o_wsale_product_images position-relative"
                         t-if="website.product_page_image_width != 'none'"
                         t-att-data-image-amount="len(product_variant._get_images() if product_variant else product._get_images())">
                        <t t-call="website_sale.shop_product_images"/>
                    </div>

                    <!-- Product Description-->
                    <div t-attf-class="col-lg-#{image_cols[1]} mt-md-2 o_cc1" id="product_details">
                        <t t-set="base_url" t-value="website.get_base_url()"/>
                        <h1 itemprop="name" t-field="product.name">Product Name</h1>
                        <span itemprop="url" style="display:none;" t-esc="base_url + product.website_url"/>
                        <span itemprop="image" style="display:none;"
                              t-esc="base_url + website.image_url(product, 'image_1920')"/>
                        <t t-if="is_view_active('website_sale.product_comment')">
                            <a href="#o_product_page_reviews" class="o_product_page_reviews_link text-decoration-none">
                                <t t-call="portal_rating.rating_widget_stars_static">
                                    <t t-set="rating_avg" t-value="product.rating_avg"/>
                                    <t t-set="trans_text_plural">%s reviews</t>
                                    <t t-set="trans_text_singular">%s review</t>
                                    <t t-set="rating_count"
                                       t-value="(trans_text_plural if product.rating_count > 1 else trans_text_singular) % product.rating_count"/>
                                </t>
                            </a>
                        </t>


                        <div t-field="product.description_ecommerce" class="oe_structure "
                             placeholder="A detailed, formatted description to promote your product on this page. Use '/' to discover more features."/>
                        <form t-if="product._is_add_to_cart_possible()" action="/shop/cart/update" method="POST">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"
                                   t-nocache="The csrf token must always be up to date."/>

                            <div class="js_product js_main_product mb-3">
                                <div>
                                    <!-- Product Price-->
                                    <t t-call="modula_web.product_price"/>

                                    <small t-if="'base_unit_price' in combination_info"
                                           class="ms-1 text-muted o_base_unit_price_wrapper d-none"
                                           groups="website_sale.group_show_uom_price">
                                        <t t-call='website_sale.base_unit_price'/>
                                    </small>

                                    <span class="h6 text-muted">
                                        inc GST
                                    </span>

                                </div>

                                <!--Configuration-->
                                <t t-call="modula_web.product_configurator_sku">
                                    <t t-set="skus_product_ids" t-value="skus_product_ids"/>
                                    <t t-set="product" t-value="product"/>
                                </t>

                                <!--Variants-->
                                <t t-placeholder="select">
                                    <input type="hidden" class="product_id" name="product_id"
                                           t-att-value="product_variant.id"/>
                                    <input type="hidden" class="product_template_id" name="product_template_id"
                                           t-att-value="product.id"/>
                                    <input t-if="product.public_categ_ids.ids" type="hidden" class="product_category_id"
                                           name="product_category_id" t-att-value="product.public_categ_ids.ids[0]"/>
                                    <t t-call="modula_web.variants">
                                        <t t-set="ul_class" t-valuef="flex-column"/>
                                        <t t-set="parent_combination" t-value="None"/>
                                        <t t-set="combination" t-value="combination_info['combination']"/>
                                        <t t-set="second_part" t-value="combination_indices_second_part"/>
                                    </t>
                                </t>

                                <p t-if="True" class="css_not_available_msg alert alert-warning">This combination does
                                    not exist.
                                </p>

                                <!--Add to Cart-->
                                <div t-if="not product.is_range" id="o_wsale_cta_wrapper"
                                     class="d-flex flex-wrap align-items-center">
                                    <t t-set="hasQuantities" t-value="false"/>
                                    <t t-set="hasBuyNow" t-value="false"/>
                                    <!-- TODO: remove line below in master -->
                                    <t t-set="ctaSizeBig" t-value="not hasQuantities or not hasBuyNow"/>
                                    <div id="add_to_cart_wrap"
                                         t-attf-class="{{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} mt-1 w-100 align-items-center me-auto">
                                        <a data-animation-selector=".o_wsale_product_images" role="button"
                                           id="add_to_cart"
                                           t-attf-class="btn btn-primary js_check_product a-submit flex-grow-1"
                                           href="#">
                                            <i class="fa fa-shopping-cart me-2"/>
                                            Add to cart
                                            <span id="updated_price"/>
                                        </a>
                                    </div>

                                    <!-- After Pay & ZIP Logo-->

                                    <div t-attf-class="col-12 text-center mt-md-2 d-none">
                                        <img src="/modula_web/static/src/svg/after-pay.svg" alt="AfterPay"
                                             width="75" height="14"/>

                                        <img src="/modula_web/static/src/svg/zip.svg" alt="Zip"
                                             width="75" height="14"/>

                                    </div>

                                    <!--Add to wishlist-->
                                    <div id="product_option_block" class="d-flex flex-wrap w-100"
                                         style="display:none !important"/>
                                </div>

                                <!--Product Tags-->
                                <!--                                <t t-if="is_view_active('website_sale.product_tags')"-->
                                <t t-call="modula_web.product_tags">
                                    <t t-set="all_product_tags" t-value="product_variant.all_product_tag_ids"/>
                                </t>
                            </div>
                        </form>
                        <p t-elif="not product.active" class="alert alert-warning">
                            This product is no longer available.
                        </p>
                        <p t-else="" class="alert alert-warning">
                            This product has no valid combination.
                        </p>
                        <t t-call="website_sale.product_accordion"/>
                        <div id="contact_us_wrapper"
                             t-attf-class="{{'d-flex' if combination_info['prevent_zero_price_sale'] else 'd-none'}} oe_structure oe_structure_solo #{_div_classes}">
                            <section class="s_text_block" data-snippet="s_text_block" data-name="Text">
                                <div class="container">
                                    <a t-att-href="website.contact_us_button_url" class="btn btn-primary btn_cta">
                                        Contact Us
                                    </a>
                                </div>
                            </section>
                        </div>
                        <div t-if="not is_view_active('website_sale_comparison.accordion_specs_item')"
                             id="product_attributes_simple">
                            <t t-set="single_value_attributes"
                               t-value="product.valid_product_template_attribute_line_ids._prepare_single_value_for_display()"/>
                            <table t-attf-class="table table-sm text-muted {{'' if single_value_attributes else 'd-none'}}">
                                <t t-foreach="single_value_attributes" t-as="attribute">
                                    <tr>
                                        <td>
                                            <span t-field="attribute.name"/>:

                                            <t t-foreach="single_value_attributes[attribute]" t-as="ptal">
                                                <span t-field="ptal.product_template_value_ids._only_active().name"/>
                                                <t t-if="not ptal_last">,</t>
                                            </t>
                                        </td>
                                    </tr>
                                </t>
                            </table>
                        </div>
                        <t t-set="product_documents"
                           t-value="product.sudo().product_document_ids.filtered(lambda doc: doc.shown_on_product_page)"/>
                        <div id="product_documents" class="my-2" t-if="product_documents">
                            <h5>Documents</h5>
                            <t t-foreach="product_documents" t-as="document_sudo">
                                <t t-set="attachment_sudo" t-value="document_sudo.ir_attachment_id"/>
                                <t t-set="target" t-value="attachment_sudo.type == 'url' and '_blank' or '_self'"/>
                                <t t-set="icon" t-value="attachment_sudo.type == 'url' and 'fa-link' or 'fa-download'"/>
                                <div>
                                    <a t-att-href="'/shop/' + slug(product) + '/document/' + str(document_sudo.id)"
                                       t-att-target="target">
                                        <i t-att-class="'fa ' + icon"/>
                                        <t t-out="attachment_sudo.name"/>
                                    </a>
                                </div>
                            </t>
                        </div>


                        <div id="o_product_terms_and_share"
                             class="d-none d-flex justify-content-between flex-column flex-md-row align-items-md-end gap-3 mb-3"/>


                    </div>


                </div>


                <!--Product Dimensions & Specifications -->

                <!--Show this section only if the product is not range-->
                <t t-if="not product.is_range">
                    <t t-call="modula_web.dimension_and_specification"/>
                </t>


            </section>

            <!-- Product Static Marketing Content-->
            <section class=" pt48 pb24">
                <t t-call="modula_web.marketing_content"/>
            </section>
        </xpath>

        <!--Remove unwanted div section from modula_product-->
        <xpath expr="//div[@id='product_full_description']" position="attributes">

            <attribute name="style">display:none !important;</attribute>

        </xpath>

        <!--Remove unwanted div section from modula_product-->
        <xpath expr="//div[@id='oe_structure_website_sale_product_2']" position="attributes">

            <attribute name="style">display:none !important;</attribute>

        </xpath>


    </template>


    <template id="product_content" name="Product Additional Content">
        <div t-attf-class="col-lg-6 o_colored_level product_content {{bg_color}}">
            <!--            <div class="col-lg-6 o_colored_level o_cc o_cc4 product_content"-->
            <!--             t-attf-style="background-color: {{ bg_color }};">-->
            <h6 class="text-uppercase">
                <t t-esc="first_title"/>
            </h6>

            <h2 class="h3-fs mt-3">
                <t t-esc="second_title"/>
            </h2>

            <p class="mt-5">
                <t t-esc="description"/>
            </p>

            <a href="#" class="btn btn-primary o_default_snippet_text" t-if="has_button" style="border-radius: 2rem;
      background: transparent !important;
      border: 1px solid white;">
                <t t-out="has_button"/>
            </a>
        </div>
    </template>


    <template id="product_media" name="Product Additional Media Content">
        <div class="col-lg-6 o_cc o_cc4 o_colored_level pt168 oe_img_bg o_bg_img_center o_bg_img_origin_border_box"
             t-attf-style="background-image: url({{image_url}});"
             data-mimetype="image/webp">
        </div>
    </template>


    <!--    Product Price Template-->
    <template id="product_price" name="Product Price Information">

        <div itemprop="offers" itemscope="itemscope" itemtype="http://schema.org/Offer"
             t-attf-class="mt-0.2 product_price {{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-block'}} oe_product_price">
            <h5 class="product_price css_editable_mode_hidden fw-400 mt-2d">
                <span t-if="product.is_range" style="font-weight: 300 !important;">From</span>
                <span class="oe_price"
                      style="white-space: nowrap;"
                      t-out="combination_info['price']"
                      t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                <span itemprop="price" style="display:none;" t-out="combination_info['price']"/>
                <span itemprop="priceCurrency" style="display:none;" t-esc="website.currency_id.name"/>
                <span t-attf-class="text-muted oe_default_price ms-1 {{'' if combination_info['has_discounted_price'] and not combination_info['compare_list_price'] else 'd-none'}}"
                      style="text-decoration: line-through; white-space: nowrap; font-style: italic;"
                      t-esc="combination_info['list_price']"
                      t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"
                      itemprop="listPrice"
                />
                <t t-if="is_view_active('website_sale.tax_indication')" t-call="website_sale.tax_indication"/>
                <del t-if="combination_info['compare_list_price'] and (combination_info['compare_list_price'] &gt; combination_info['price'])"
                     class="text-muted ms-1 oe_compare_list_price">
                    <bdi dir="inherit">
                        <span t-esc="combination_info['compare_list_price']"
                              groups="website_sale.group_product_price_comparison"
                              t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                    </bdi>
                </del>
            </h5>
            <h3 class="css_non_editable_mode_hidden decimal_precision"
                t-att-data-precision="str(website.currency_id.decimal_places)">
                <span t-field="product.list_price"
                      t-options="{'widget': 'monetary', 'display_currency': product.currency_id}"/>
                <t t-if="is_view_active('website_sale.tax_indication')" t-call="website_sale.tax_indication"/>
                <del t-if="combination_info['compare_list_price'] and (combination_info['compare_list_price'] &gt; combination_info['price'])">
                    <bdi dir="inherit">
                        <span t-field="product.compare_list_price"
                              groups="website_sale.group_product_price_comparison"
                              t-options="{'widget': 'monetary', 'display_currency': product.currency_id}"/>
                    </bdi>
                </del>
            </h3>
            <small t-if="combination_info.get('tax_disclaimer')" class="text-muted">
                <t t-out="combination_info['tax_disclaimer']"/>
            </small>
        </div>
        <div id="product_unavailable"
             t-attf-class="{{'d-flex' if combination_info['prevent_zero_price_sale'] else 'd-none'}}">
            <h3 class="fst-italic" t-field="website.prevent_zero_price_sale_text"/>
        </div>

    </template>

    <!--    Product Tags-->
    <template id="product_tags" name="Product Tags" active="True">
        <div class="o_product_tags o_field_tags d-flex flex-wrap align-items-center gap-2 mb-2 mt-1"
             style="display: none !important">
            <t t-foreach="all_product_tags" t-as="tag">
                <t t-if="tag.visible_on_ecommerce">
                    <span t-if="tag.image"
                          class="order-0"
                          t-field="tag.image"
                          t-options="{'widget': 'image', 'class': 'o_product_tag_img rounded'}"/>
                    <span t-else="" class="position-relative order-1 py-1 px-2">
                        <span class="position-absolute top-0 start-0 w-100 h-100 rounded"
                              t-attf-style="background-color: #{tag.color}; opacity: .2;"/>
                        <span class="text-nowrap small"
                              t-attf-style="color: #{tag.color}"
                              t-field="tag.name"/>
                    </span>
                </t>
            </t>
        </div>
    </template>


    <template id="product_configurator_sku">
        <div class="sku-configurator mt-1" t-if="skus_product_ids">

            <div>
                <span class="fw-600">Configuration :</span>
            </div>

            <div class="sku_list mt-1">
                <ul class="btn-group-toggle list-inline list-unstyled o_wsale_product_attribute ">
                    <t t-foreach="skus_product_ids" t-as="sku">

                        <a itemprop="name" class="text-decoration-none"
                           t-att-href="sku.website_url">
                            <li t-attf-class="skus_pills btn list-inline-item js_attribute_value #{'active' if product.name==sku.name else ''}">
                                <!--                                <span t-att-content="sku.name" t-field="sku.name"/>-->
                                <span t-esc="sku.name.split('-', 1)[1] if '-' in sku.name else sku.name"/>
                            </li>
                        </a>

                    </t>
                </ul>

            </div>
            <div class="sku-details d-none">
                <!-- SKU details will be loaded here when selected -->
            </div>
        </div>
    </template>


    <!-- Product quantity-->
    <!--    <template id="product_quantity" inherit_id="website_sale.product" name="Select Quantity">-->
    <!--        <xpath expr="//t[@t-set='hasQuantities']" position="attributes">-->
    <!--            <attribute name="t-value" remove="false" add="true" separator=" "/>-->
    <!--        </xpath>-->
    <!--        <div id="add_to_cart_wrap" position="before">-->
    <!--            <div t-attf-class="css_quantity input-group {{'d-none' if combination_info['prevent_zero_price_sale'] else 'd-inline-flex'}} me-2 mb-2 align-middle"-->
    <!--                 contenteditable="false">-->
    <!--                <a t-attf-href="#" class="btn btn-link js_add_cart_json" aria-label="Remove one" title="Remove one">-->
    <!--                    <i class="fa fa-minus"/>-->
    <!--                </a>-->
    <!--                <input type="text" class="form-control quantity text-center" data-min="1" name="add_qty"-->
    <!--                       t-att-value="1"/>-->
    <!--                <a t-attf-href="#" class="btn btn-link float_left js_add_cart_json" aria-label="Add one"-->
    <!--                   title="Add one">-->
    <!--                    <i class="fa fa-plus"/>-->
    <!--                </a>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--    </template>-->


    <!-- Dimension and Specification-->
    <template id="dimension_and_specification">
        <div class="row">
            <div t-attf-class="col-lg-#{image_cols[0]}" style="background: #FFFFFF">
                <div class="text-center mt48">
                    <p class="fw-500">DIMENSIONS</p>
                </div>

                <t t-set="dimension_img_url" t-value="get_product_dimension(product)"></t>

                <div class="text-center dimension_img mt48 pb48">

                    <t t-if="dimension_img_url">
                        <img t-att-src="dimension_img_url"
                             alt="Dimension Image"
                             style="height: 100%; object-fit: cover;"/>
                    </t>

                    <t t-else="">

                        <img src="/modula_web/static/src/img/default.png" alt="Dimension Image"
                             style="width: 60%;"/>

                    </t>
                </div>
            </div>

            <div t-attf-class="col-lg-#{image_cols[1]} " style="background: #f5f5f5">
                <div class="text-center mt48 mb48">
                    <p class="fw-500">SPECIFICATIONS</p>
                </div>

                <div>
                    <section class="s_accordion_image pb56 o_cc o_colored_level"
                             data-snippet="s_accordion_image" data-name="Accordion Image">
                        <div class="container-fluid">
                            <div class="row align-items-start">
                                <div class="col-12 o_colored_level">
                                    <div data-name="Accordion" data-snippet="s_accordion"
                                         class="s_accordion">
                                        <div id="myCollapse" class="accordion-flush accordion">

                                            <div class="accordion-item position-relative z-1 "
                                                 data-name="Accordion Item">

                                                <button type="button"
                                                        class="accordion-header accordion-button justify-content-between gap-2 bg-transparent h6-fs fw-bold text-decoration-none text-reset transition-none"
                                                        data-bs-toggle="collapse" aria-expanded="false"
                                                        id="accordion-button204115_1"
                                                        data-bs-target="#myCollapseTab_1"
                                                        aria-controls="myCollapseTab_1">
                                                    <span class="flex-grow-1 fw-300 text-uppercase">Feature
                                                        Overview
                                                    </span>
                                                </button>
                                                <!--Add class show to open it by default-->
                                                <div class="accordion-collapse collapse show"
                                                     data-bs-parent="#myCollapse" role="region"
                                                     id="myCollapseTab_1"
                                                     aria-labelledby="accordion-button204115_1">
                                                    <div class="accordion-body">
                                                        <p class="fw-500">Package Includes a choise, two
                                                            corner
                                                            modules, and three chair modules.
                                                        </p>

                                                        <ul>
                                                            <li class="mt-1">2024 Red Rot Design Winner,
                                                                2023
                                                                Good Design
                                                                Award Winner.
                                                            </li>
                                                            <li class="mt-1">Modern reimagining of the
                                                                original
                                                                King Living
                                                                sofa from the seventies.
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="accordion-item position-relative z-1"
                                                 data-name="Accordion Item">
                                                <button type="button"
                                                        class="accordion-header accordion-button justify-content-between gap-2 bg-transparent h6-fs fw-bold text-decoration-none text-reset transition-none collapsed"
                                                        data-bs-toggle="collapse" aria-expanded="false"
                                                        id="accordion-button204115_2"
                                                        data-bs-target="#myCollapseTab_2"
                                                        aria-controls="myCollapseTab_2">
                                                    <span class="flex-grow-1 fw-300 text-uppercase">Shipping
                                                        &amp; Delivery
                                                    </span>
                                                </button>
                                                <div class="accordion-collapse collapse"
                                                     data-bs-parent="#myCollapse" role="region"
                                                     id="myCollapseTab_2"
                                                     aria-labelledby="accordion-button204115_2">
                                                    <div class="accordion-body">
                                                        <p>You can reach our customer support team by
                                                            emailing
                                                            <EMAIL>, calling +1
                                                            555-555-5556, or using the live chat on our
                                                            website.
                                                            Our dedicated team is available 24/7 to assist
                                                            with
                                                            any inquiries or issues.
                                                        </p>
                                                        <p>We’re committed to providing prompt and effective
                                                            solutions to ensure your satisfaction.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="accordion-item position-relative z-1"
                                                 data-name="Accordion Item">
                                                <button type="button"
                                                        class="accordion-header accordion-button justify-content-between gap-2 bg-transparent h6-fs fw-bold text-decoration-none text-reset transition-none collapsed"
                                                        data-bs-toggle="collapse" aria-expanded="false"
                                                        id="accordion-button204115_2"
                                                        data-bs-target="#myCollapseTab_3"
                                                        aria-controls="myCollapseTab_3">
                                                    <span class="flex-grow-1 fw-300 text-uppercase">Warranty
                                                    </span>
                                                </button>
                                                <div class="accordion-collapse collapse"
                                                     data-bs-parent="#myCollapse" role="region"
                                                     id="myCollapseTab_3"
                                                     aria-labelledby="accordion-button204115_2">
                                                    <div class="accordion-body">
                                                        <p>You can reach our customer support team by
                                                            emailing
                                                            <EMAIL>, calling +1
                                                            555-555-5556, or using the live chat on our
                                                            website.
                                                            Our dedicated team is available 24/7 to assist
                                                            with
                                                            any inquiries or issues.
                                                        </p>
                                                        <p>We’re committed to providing prompt and effective
                                                            solutions to ensure your satisfaction.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="accordion-item position-relative z-1"
                                                 data-name="Accordion Item">
                                                <button type="button"
                                                        class="accordion-header accordion-button justify-content-between gap-2 bg-transparent h6-fs fw-bold text-decoration-none text-reset transition-none collapsed"
                                                        data-bs-toggle="collapse" aria-expanded="false"
                                                        id="accordion-button204115_2"
                                                        data-bs-target="#myCollapseTab_4"
                                                        aria-controls="myCollapseTab_4">
                                                    <span class="flex-grow-1 fw-300 text-uppercase">Product
                                                        Support
                                                    </span>
                                                </button>
                                                <div class="accordion-collapse collapse"
                                                     data-bs-parent="#myCollapse" role="region"
                                                     id="myCollapseTab_4"
                                                     aria-labelledby="accordion-button204115_2">
                                                    <div class="accordion-body">
                                                        <p>You can reach our customer support team by
                                                            emailing
                                                            <EMAIL>, calling +1
                                                            555-555-5556, or using the live chat on our
                                                            website.
                                                            Our dedicated team is available 24/7 to assist
                                                            with
                                                            any inquiries or issues.
                                                        </p>
                                                        <p>We’re committed to providing prompt and effective
                                                            solutions to ensure your satisfaction.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>


                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </template>


    <!-- Product marketing content-->
    <template id="marketing_content">

        <div class="container">
            <div class="row s_nb_column_fixed marketing_content">

                <div class="col-lg-12 pb48 o_colored_level text-center o_animate o_anim_fade_in o_animate_both_scroll">
                    <h2 class="o_default_snippet_text">
                        <span>Crafted for Comfort,</span>
                        <br/>
                        <span>Designed for Modern Australian Living</span>
                    </h2>
                </div>


                <div class="grid-container">

                    <!--First-->
                    <div class="grid-item o_animate o_anim_fade_in o_animate_both_scroll">
                        <div class="image-wrapper mb-1">
                            <img src="/web/image/website.s_key_images_default_image_2"
                                 class="img img-fluid zoom-hover"
                                 alt="" style="width: 100% !important;" loading="lazy"
                                 data-mimetype="image/jpeg"
                                 data-original-id="692"
                                 data-original-src="/website/static/src/img/snippets_demo/s_key_images_default_image_2.jpg"
                                 data-mimetype-before-conversion="image/jpeg"/>
                        </div>
                        <h3>Modular Lounges</h3>
                        <p class="o_default_snippet_text">Your home should be as flexible as your life. Our true modular
                            sofas allow you to customise layouts and reconfigure them as your needs change, offering
                            complete freedom to shape your space.
                        </p>
                    </div>

                    <!--Second-->
                    <div class="grid-item o_animate o_anim_fade_in o_animate_both_scroll">
                        <div class="image-wrapper pdp-m-6 mb-1">
                            <img src="/web/image/website.s_key_images_default_image_1"
                                 class="img img-fluid zoom-hover"
                                 alt="" style="width: 100% !important;" loading="lazy"
                                 data-mimetype="image/jpeg"
                                 data-original-id="691"
                                 data-original-src="/website/static/src/img/snippets_demo/s_key_images_default_image_1.jpg"
                                 data-mimetype-before-conversion="image/jpeg"/>
                        </div>
                        <h3>Thoughtful Craftsmanship</h3>
                        <p class="o_default_snippet_text ">We are dedicated to quality in every detail. From 100%
                            leather and premium fabrics to durable frames and smooth mechanisms, our lounges are made
                            for lasting comfort and timeless appeal.
                        </p>
                    </div>

                    <!--Third-->
                    <div class="grid-item o_animate o_anim_fade_in o_animate_both_scroll">
                        <div class="image-wrapper mb-1">
                            <img src="/web/image/website.s_key_images_default_image_4"
                                 class="img img-fluid zoom-hover"
                                 alt="" style="width: 100% !important;" loading="lazy"
                                 data-mimetype="image/jpeg"
                                 data-original-id="694"
                                 data-original-src="/website/static/src/img/snippets_demo/s_key_images_default_image_4.jpg"
                                 data-mimetype-before-conversion="image/jpeg"/>
                        </div>
                        <h3>Smart Features</h3>
                        <p class="o_default_snippet_text">We combine elegant design with modern convenience, integrating
                            features like electric motion and wireless charging to elevate your experience and bring
                            effortless comfort into your home.
                        </p>
                    </div>

                    <!-- Fourth-->
                    <div class="grid-item o_animate o_anim_fade_in o_animate_both_scroll">
                        <div class="image-wrapper pdp-m-6 mb-1">
                            <img src="/web/image/website.s_key_images_default_image_3"
                                 class="img img-fluid zoom-hover"
                                 alt="" style="width: 100% !important;" loading="lazy"
                                 data-mimetype="image/jpeg"
                                 data-original-id="693"
                                 data-original-src="/website/static/src/img/snippets_demo/s_key_images_default_image_3.jpg"
                                 data-mimetype-before-conversion="image/jpeg"/>
                        </div>
                        <h3>Customer First</h3>
                        <p class="o_default_snippet_text">Every decision we make starts with your needs. From product
                            design to after-sales support, we’re committed to delivering a seamless experience that puts
                            your comfort, convenience, and satisfaction first.
                        </p>
                    </div>
                </div>

            </div>
        </div>
    </template>

</odoo>