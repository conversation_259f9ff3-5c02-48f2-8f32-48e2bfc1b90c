<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="home_extended" inherit_id="website.homepage" name="Custom Home Page" active="True">

        <!--Home page-->
        <xpath expr="//div[@id='wrap']" position="replace">
            <t t-call="modula_web.custom_home_page_template"/>
        </xpath>


    </template>

    <template id="custom_layout" inherit_id="website.layout" name="Custom Website Layout" active="True"
              customize_show="False">

        <!--Page Pre Loader-->
        <xpath expr="//body" position="inside">
            <div id="preloader"/>
        </xpath>

        <!--Custom Header-->
        <xpath expr="//header" position="replace">
            <t t-call="modula_web.header_template"/>

            <!--Overlay Background For Mega Menu-->
            <div class="fixed-overlay"></div>

        </xpath>

        <!--Custom Footer-->
        <xpath expr="//div[@id='footer']" position="replace">
            <t t-call="modula_web.custom_footer_template"/>
        </xpath>

        <xpath expr="//div[@class='o_footer_copyright o_colored_level o_cc']" position="replace">
            <div class="o_footer_copyright o_colored_level o_cc" t-attf-style="display: none">
                <div class="container py-3">
                    <div class="row">
                        <div class="col-sm text-center text-sm-start text-muted"
                             t-attf-style="text-align: center !important">
                            <span class="o_footer_copyright_name me-2">Copyright &amp;copy;
                                Modula Living , 2025
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </xpath>

    </template>


    <!-- Customize this for each page title-->
    <template id="layout" name="Main layout" inherit_id="portal.frontend_layout">

        <xpath expr="//head/*[1]" position="before">
            <t t-if="not title">
                <t t-if="not additional_title and main_object and 'name' in main_object">
                    <t t-set="additional_title" t-value="main_object.name"/>
                </t>
                <t t-set="default_title" t-translation="off"
                   t-value="(additional_title + ' | ' if additional_title else '') + website.name"/>
                <t t-set="seo_object" t-value="seo_object or main_object"/>
                <t t-if="seo_object and 'website_meta_title' in seo_object and seo_object.website_meta_title">
                    <t t-set="title" t-value="seo_object.website_meta_title"/>
                </t>
                <t t-else="">
                    <t t-set="title" t-value="default_title"></t>
                </t>
            </t>
            <t t-set="x_icon" t-value="website.image_url(website, 'favicon')"/>
        </xpath>

    </template>

</odoo>