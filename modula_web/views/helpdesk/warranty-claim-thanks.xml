<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="website_helpdesk_thanks_inherit" inherit_id="website_helpdesk.ticket_submited">

        <xpath expr="//t[@t-call='website.layout']" position="replace">
            <t t-call="website.layout">
                <div id="wrap" class="oe_structure oe_empty h-100">
                    <div class="container d-flex flex-column justify-content-center h-100">
                        <div class="oe_structure" id="oe_structure_website_helpdesk_thanks_you"/>
                        <div class="d-flex flex-column align-items-center mb16 p-4 text-center">
                            <t t-if="request.session.get('form_builder_model_model', '') == 'helpdesk.ticket'">
                                <t t-set="ticket" t-value="request.website._website_form_last_record()"/>
                            </t>
                            <!--                            <i class="fa fa-paper-plane fa-2x mb-3 rounded-circle text-bg-success" role="presentation"/>-->

                            <div class="container s_allow_columns">
                                <div class="row">
                                    <div class="col-lg-8 offset-lg-2 text-center">
                                        <!-- Warranty Claim-->
                                        <t t-if="ticket.name=='Warranty Claim'">
                                            <h1 class="fw-bolder mt-2">Your warranty claim has been received!</h1>
                                            <t t-if="ticket">
                                                <p class="lead mb-2">Thank you for submitting your warranty claim. Our
                                                    support team
                                                    will carefully review your information and get back to you promptly
                                                    with the
                                                    next steps. We appreciate your patience and assure you we’ll do
                                                    everything we
                                                    can to assist you swiftly.
                                                </p>
                                                <p>
                                                    <a t-if="request.session.uid"
                                                       class="my-3 border rounded px-4 py-3 bg-100 fs-5 fw-bold shadow-sm text-decoration-none"
                                                       t-attf-href="/helpdesk/ticket/#{ticket.id}"
                                                       t-att-title="'Ticket #' + ticket.sudo().ticket_ref">
                                                        Ticket #
                                                        <span t-field="ticket.sudo().ticket_ref"/>
                                                    </a>
                                                    <span t-if="not request.session.get('uid')"
                                                          class="my-3 border rounded px-4 py-3 fs-5 fw-bold shadow-sm">
                                                        Ticket #
                                                        <span t-field="ticket.sudo().ticket_ref"/>
                                                    </span>
                                                </p>
                                                <p class="mt-2">
                                                    <a href="/">Go to Homepage</a>
                                                </p>
                                            </t>
                                        </t>

                                        <t t-else="">
                                            <h1 class="fw-bolder">Thank you!</h1>
                                            <t t-if="ticket">
                                                <p class="lead mb-0">Your ticket has been sent.</p>
                                                <p class="lead">Our team will get right on it.</p>
                                                <a t-if="request.session.uid"
                                                   class="my-3 border rounded px-4 py-3 bg-100 fs-5 fw-bold shadow-sm text-decoration-none"
                                                   t-attf-href="/helpdesk/ticket/#{ticket.id}"
                                                   t-att-title="'Ticket #' + ticket.sudo().ticket_ref">
                                                    Ticket #
                                                    <span t-field="ticket.sudo().ticket_ref"/>
                                                </a>
                                                <span t-if="not request.session.get('uid')"
                                                      class="my-3 border rounded px-4 py-3 fs-5 fw-bold shadow-sm">
                                                    Ticket #
                                                    <span t-field="ticket.sudo().ticket_ref"/>
                                                </span>
                                            </t>
                                            <a href="/">Go to Homepage</a>
                                        </t>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="oe_structure" id="oe_structure_website_helpdesk_thanks_you_bottom"/>
                    </div>
                </div>
            </t>

        </xpath>
    </template>
</odoo>

