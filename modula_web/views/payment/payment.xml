<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <!--    Payment Layout-->
    <template id="custom_payment_layout" inherit_id="website_sale.payment" name="Custom checkout layout">
        <xpath expr="//t[@t-call='website_sale.checkout_layout']" position="replace">

            <t t-call="website_sale.checkout_layout">
                <t t-set="additional_title">Shop - Select Payment Method</t>
                <t t-set='redirect' t-valuef="/shop/payment"/>
                <!--Footer is enabled-->
                <t t-set="show_footer" t-value="True"/>
                <t t-set="oe_structure">
                    <!-- This is the drag-and-drop area for website building blocs at the end of each
                         checkout page. This is append at the of the page in `checkout_layout`. The
                         templates created in the database to store blocs are hooked using XPath on the
                         `oe_struture` element ID. Therefore, we can't use dynamic IDs (like with
                         t-att-id) and each template needs to define a div element. -->
                    <div class="oe_structure" id="oe_structure_website_sale_payment_2"/>
                </t>
                <div class="col-12" t-if="errors">
                    <t t-set="hide_payment_button" t-value="True"/>
                    <t t-foreach="errors" t-as="error">
                        <div class="alert alert-success" t-if="error" role="alert">
                            <p>
                                <strong>
                                    <t t-esc="error[0]"/>
                                </strong>
                            </p>
                            <t t-esc="error[1]"/>
                        </div>
                    </t>
                </div>
                <h3 class="mb-4">Confirm order</h3>
                <div id="address_on_payment" class="mb-4">
                    <t t-call="website_sale.address_on_payment"/>
                </div>
                <div class="oe_structure clearfix mt-3" id="oe_structure_website_sale_payment_1"/>

                <div t-if="not errors and website_sale_order.amount_total"
                     name="website_sale_non_free_cart">
                    <div id="payment_method" class="o_not_editable mt-4">
                        <t t-call="payment.form"/>
                    </div>
                    <t t-if="not (payment_methods_sudo or tokens_sudo)" t-set="hide_payment_button"
                       t-value="True"/>
                </div>
            </t>

        </xpath>


    </template>
</odoo>