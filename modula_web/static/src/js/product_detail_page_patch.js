/** @odoo-module **/

import publicWidget from '@web/legacy/js/public/public_widget';

publicWidget.registry.ProductFormResultPatch = publicWidget.Widget.extend({
    selector: '.oe_product_price',

    start: function () {
        this._super.apply(this, arguments);
        this._observePriceChange();
    },

    _observePriceChange: function () {
        const priceSpan = document.querySelector('.oe_price');
        if (!priceSpan) return;

        const observer = new MutationObserver((mutationsList) => {
            for (const mutation of mutationsList) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    // console.log("Price changed to:", priceSpan.textContent.trim());
                    this._onPriceChanged(priceSpan.textContent.trim());
                }
            }
        });

        observer.observe(priceSpan, {
            childList: true,
            subtree: true,
            characterData: true,
        });

        this._priceObserver = observer; // Save it if needed later
    },

    _onPriceChanged: function (newPrice) {
        const el = document.getElementById('updated_price');
        if (el) {
            el.innerText = '- ' + newPrice;
        }
    },
});
