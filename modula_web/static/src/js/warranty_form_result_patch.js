/** @odoo-module **/

import publicWidget from '@web/legacy/js/public/public_widget';

publicWidget.registry.WarrantyFormResultPatch = publicWidget.Widget.extend({
    selector: '.oe_helpdesk',
    events: {
        "click .s_website_form_send": "_onSubmitCustomErrorHandler",
    },

    _onSubmitCustomErrorHandler: function () {

        const originalResultEl = document.getElementById('s_website_form_result');
        originalResultEl.style.display = 'none';
        // const customResultEl = document.getElementById('custom_s_website_form_result');

        setTimeout(() => {

            if (!originalResultEl) return;

            originalResultEl.style.display = 'block';

            console.log(originalResultEl.innerText)

            let msg = originalResultEl.innerText.trim();

            // if (!msg) {
            //     customResultEl.innerText = '';
            //     return;
            // }

            const defaultMsg = 'Please fill in the form correctly.';

            console.log(msg)

            // If message is only the default one, hide both

            // Remove the default part if it's at the beginning
            if (msg.startsWith(defaultMsg)) {
                msg = msg.replace(defaultMsg, '').trim();
            }

            // Remove leading punctuation (like a period or comma) if left behind
            msg = msg.replace(/^[.,\s]+/, '');

            originalResultEl.innerHTML = '<i class="fa fa-close me-1" role="img" aria-hidden="true" title="Error"> </i> ' + msg;


            // Hide the original message
            // originalResultEl.style.display = 'none';
        }, 100); // Wait for the error message to be rendered
    }
});
