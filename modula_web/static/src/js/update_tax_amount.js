/** @odoo-module **/

import publicWidget from '@web/legacy/js/public/public_widget';

publicWidget.registry.WarrantyFormResultPatch = publicWidget.Widget.extend({
    selector: '#cart_total',

    start: function () {
        this._super.apply(this, arguments);
        this._observeTaxChange();
    },

    _observeTaxChange: function () {
        const taxElement = this.el.querySelector('#cart_tax_amount');
        const newTaxElement = document.getElementById('updated_tax_amount');

        // Initially set same as cart tax amount
        newTaxElement.textContent = taxElement.textContent.replace(/\s+/g, '');

        console.log(newTaxElement.textContent)


        if (!taxElement) {
            console.warn('Tax element not found');
            return;
        }

        const observer = new MutationObserver(() => {
            const taxValue = taxElement.textContent.replace(/\s+/g, '');
            if (newTaxElement) {
                newTaxElement.textContent = taxValue;
            }
        });

        observer.observe(taxElement, {
            characterData: true,
            childList: true,
            subtree: true,
        });

        // Optional: Store the observer if you need to disconnect it later
        this._taxObserver = observer;
    },

    destroy: function () {
        if (this._taxObserver) {
            this._taxObserver.disconnect();
        }
        this._super.apply(this, arguments);
    },

    _onSubmitCustomErrorHandler: function (ev) {
        // Your custom error handling logic here
    },
});
