/** @odoo-module **/
import publicWidget from '@web/legacy/js/public/public_widget';


publicWidget.registry.ModulaWeb = publicWidget.Widget.extend({

    selector: "#header",

    events: {
        'mouseenter .mega-nav-item.has-submenu': '_onSubmenuMouseEnter',
        'mouseleave .mega-nav-item.has-submenu': '_onNavItemMouseLeave',
    },

    start: function () {
        this._super.apply(this, arguments);
        this.navs = this.el.querySelectorAll('nav');
        this.transparentClass = 'navbar_transparent';
        this.whiteLogoSrc = '/modula_web/static/src/img/website_logo_white.webp';
        this.blackLogoSrc = '/modula_web/static/src/img/website_logo_black.webp';
        this.logo = document.getElementById('logo');
        this.logoMob = document.getElementById('logo_mob');
        this.body = document.body;

        this.body.classList.add('no-hover');

        // Remove 'combination' query parameter from the URL
        // this._removeCombinationParam();

        // window.scrollTo(0, 0);
        // Scroll to the top of the page
        // window.scrollTo({
        //     top: 0,
        //     left: 0,
        //     behavior: 'smooth'
        // });
        // this.submenus = this.el.querySelectorAll('.submenu');
        // this._subMenuEvent();

        if (window.location.pathname == '/') {

            window.scrollTo({
                top: 0,
                left: 0,
                behavior: 'smooth'
            });

            const navbar = document.querySelector('.navbar');
            if (navbar) {
                navbar.style.position = 'absolute';
            }
        }

        this.navs.forEach(nav => {
            this._bindScrollEvent(nav);
            this._setActiveNavItem(nav);
        });
        // this._bindScrollEvent();
        // this._setActiveNavItem();

    }
    ,

    /**
     * Used to disable clicks on navbar links on desktop
     * @private
     */
    _toggleDropdownBehavior: function () {
        const isTouchDevice = window.matchMedia("(hover: none) and (pointer: coarse)").matches;
        const dropdownLinks = this.el.querySelectorAll('.mega-nav-item > a');
        const dropdownToggles = document.querySelectorAll('.nav-link.dropdown-toggle');

        dropdownLinks.forEach(link => {
            if (isTouchDevice) {
                link.setAttribute('data-bs-toggle', 'dropdown');
            } else {
                link.removeAttribute('data-bs-toggle');
            }
        });

        // Add or remove class on header
        if (!isTouchDevice) {
            this.el.classList.add('o_hoverable_dropdown');
        } else {
            this.el.classList.remove('o_hoverable_dropdown');
            this.el.classList.remove('o_top_fixed_element');
        }


        dropdownToggles.forEach(function (link) {
            link.addEventListener('click', function (event) {
                event.preventDefault();
                event.stopPropagation();
                // Custom logic to toggle dropdown
            });

            link.addEventListener('touchstart', function (event) {
                // event.preventDefault();
                // event.stopPropagation();
                // Custom logic to toggle dropdown
            });
        });

    }
    ,


    /**
     * Handles the click event on navigation items.
     * @private
     */
    _onNavItemClick: function (event) {
        event.preventDefault();
        this.$('.navbar-nav .nav-item').removeClass('active');
        this.$(event.currentTarget).closest('.nav-item').addClass('active');
        window.location.href = event.currentTarget.href;
    }
    ,

    /**
     * Sets the active class on the navigation item based on the current URL.
     * @private
     */
    _setActiveNavItem: function (nav) {
        const currentPath = window.location.pathname;
        nav.querySelectorAll('.navbar-nav .nav-item a').forEach(link => {
            const linkPath = link.getAttribute('href');
            if (currentPath === linkPath && linkPath !== '/') {
                link.closest('.nav-item').classList.add('active');
            }
        });
    }
    ,


    /**
     * Binds the scroll event to the window object.
     * @private
     */
    _bindScrollEvent: function (nav) {
        var self = this;

        if (window.location.pathname === '/') {
            nav.classList.add(this.transparentClass);
            this._setLogoSrc(this.whiteLogoSrc);
        } else {
            this._setLogoSrc(this.blackLogoSrc);
        }
        window.addEventListener('scroll', function () {
            self._onPageScroll(nav);
        });
    }
    ,

    /**
     * Handles the scroll event and checks if the page is at the top.
     * @private
     */
    _onPageScroll: function (nav) {
        // Check if the current page is the homepage
        if (window.location.pathname === '/') {
            var scrollTop = window.scrollY;
            if (scrollTop === 0) {
                console.log("Page has been scrolled to the top.");
                nav.classList.add(this.transparentClass);
                this._setLogoSrc(this.whiteLogoSrc);
                // console.log("White logo set", document.getElementById('logo').src)
            } else {
                console.log("Page has been scrolled down.");
                nav.classList.remove(this.transparentClass);
                this._setLogoSrc(this.blackLogoSrc);
            }
        } else {
            this._setLogoSrc(this.blackLogoSrc);
        }
    }
    ,

    /**
     * Displays the submenu when mouse enters the parent item.
     * @private
     */
    _onSubmenuMouseEnter: function (event) {
        this._setLogoSrc(this.blackLogoSrc);

        if (window.location.pathname === '/') {
            this.navs.forEach(nav => nav.classList.remove(this.transparentClass));
        }

        this.body.classList.add('shadow_bg');

    }
    ,

    /**
     * Hides the submenu when mouse leaves the parent item.
     * @private
     */
    _onNavItemMouseLeave: function (event) {
        const scrollTop = window.scrollY;
        if (window.location.pathname === '/' && scrollTop === 0) {
            this.navs.forEach(nav => nav.classList.add(this.transparentClass));
            this._setLogoSrc(this.whiteLogoSrc);
        } else {
            this._setLogoSrc(this.blackLogoSrc);
        }

        this.body.classList.remove('shadow_bg');
    }
    ,

    _setLogoSrc: function (src) {
        if (this.logo) this.logo.src = src;
        if (this.logoMob) this.logoMob.src = src;
    }
    ,

    scrollToSmoothly: function (yTarget = 0, duration = 600) {
        const startY = window.pageYOffset;
        const distance = yTarget - startY;
        const startTime = performance.now();

        function step(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const ease = progress < 0.5
                ? 2 * progress * progress
                : -1 + (4 - 2 * progress) * progress;
            window.scrollTo(0, startY + distance * ease);
            if (elapsed < duration) requestAnimationFrame(step);
        }

        requestAnimationFrame(step);
    }


});