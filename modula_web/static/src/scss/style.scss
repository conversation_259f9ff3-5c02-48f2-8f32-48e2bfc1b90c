body {
  background-color: $primary-background;
}

// Fix bouncing effect on scroll
//html {
//  overscroll-behavior-y: none;
//  height: 100%;
//  overflow: hidden;
//}
//
//body {
//  height: 100%;
//  overflow: auto;
//}
// End

font-inter {
  font-family: Inter !important;
}

.playfair {
  font-family: 'Playfair Display';
}

header .navbar-brand.logo img {
  //object-fit: contain;
  //display: block;
  //height: 100%;
  width: auto;
  height: auto;
  max-width: 11.5rem;
}

//
//.o_header_affixed.o_header_is_scrolled .navbar-brand img {
//  height: 100%;
//}
//
//header .navbar-brand {
//  max-width: 100%;
//}

// Navbar Transparent Styles
.navbar_transparent {
  position: absolute;
  z-index: 999;
  width: 100%;
  background: transparent !important;
  color: white;
  box-shadow: none !important;
  //transition: background-color .1s ease-out;
  //transition: all 0.5s cubic-bezier(.6, 0, .4, 1);

  .nav-item {
    .nav-link {
      color: white !important;
    }
  }

  .mega-nav-item {
    .nav-link {
      color: white !important;
    }
  }

  .hover-underline-text::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    //background: linear-gradient(to right, #ff0000, #00ffff);
    background: linear-gradient(to right, #FFFFFF, #FFFFFF);
    bottom: 0px;
    left: 0;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease-out;
  }
}

.navbar {
  background: #ffffff;
  padding: 0px;
  position: absolute;
  width: 100%;
  //padding: $navbar-padding;
  //transition: background-color 0.1s ease-in-out;
  transition: all 0.3s cubic-bezier(.6, 0, .4, 1);
}

#wrapwrap > main {
  margin-top: $navbar-height !important;
}

//.oe_structure, .wrap, #wrap {
//  margin-top: $navbar-height;
//}
//
//.oe_website_login_container {
//  margin-top: 8rem;
//}
//
//.oe_structure.with-no-top-margin {
//  margin-top: 0px !important;
//}

.o_header_mobile {
  padding: 15px !important;
}


.oe_login_title {
  max-width: 500px;
  position: relative;
  margin: 50px auto;
}

.oe_login_subtitle {
  max-width: 500px;
  margin: 0px auto;
}

.border-none {
  border: none !important;
}

.d-hide {
  position: absolute;
  clip-path: inset(100%);
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.text-capital {
  text-transform: capitalize;
}

.fw-300 {
  font-weight: 300;
}

.fw-500 {
  font-weight: 500;
}

.fw-600 {
  font-weight: 600;
}

.font-small {
  font-size: 0.875em;
}

.f-8 {
  font-size: 8px;
}

.f-14 {
  font-size: 14px;
}

.f-16 {
  font-size: 16px;
}

.f-18 {
  font-size: 18px;
}

.f-20 {
  font-size: 20px;
}

.ml-2-r {
  margin-left: 2rem;
}

.text-uppercase {
  text-transform: uppercase;
}

.nopadding {
  padding: 0px;
}

.ptb-230 {
  padding: 232px;
}

.red-text {
  color: $red-color;
}

.me-n1 {
  margin-right: -0.8rem !important;
}

.oe_currency_value {
  margin-left: -3px;
}

// Hides the error image from invalid date picker selection
#div_invoice_date {
  .form-control.is-invalid {
    border-color: var(--form-invalid-border-color);
    padding-right: calc(1.5em + 0.75rem);
    background-image: none !important;
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  }
}

//#s_website_form_result {
//  color: transparent !important;
//  display: none;
//}

@media (min-width: 768px) {
  header:not(.o_header_is_scrolled) .o_main_nav {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
  }
}

@media (max-width: 768px) {

  header .navbar-brand {
    max-width: 100%;
  }

  header .navbar-brand.logo img {
    //width: auto;
    //height: auto;
    //max-width: 7.5rem;
    width: 100%;
    height: auto;
    //max-width: 7.5rem;
  }

  h1 {
    font-size: 32px !important;
  }

  h2 {
    font-size: 24px !important;
  }

  h3 {
    font-size: 20px !important;
  }

  h4 {
    font-size: 18px !important;
  }

  h5 {
    font-size: 18px !important;
  }

  h6 {
    font-size: 16px !important;
  }

  p, a {
    font-size: 15px !important;
  }

  small {
    font-size: 12px !important;
  }

  .pb50 {
    padding-bottom: 25px !important;
  }

  .pt70 {
    padding-top: 35px !important;
  }

  .f-8 {
    font-size: 6px !important;
  }

  .f-14 {
    font-size: 12px !important;
  }

  .f-16 {
    font-size: 14px !important;
  }

  .f-18 {
    font-size: 16px !important;
  }

  .f-20 {
    font-size: 18px !important;
  }


}

// Header Affixed Styles
.o_header_affixed {
  z-index: 9999;
  transform: translate(0px, 0px) !important;
  //transition: all 0.5s cubic-bezier(.6, 0, .4, 1);

  &:not(.o_header_no_transition) {
    transition: transform 0ms !important;
  }
}


// Navbar Link Styles
.nav-item {
  a.nav-link {
    color: $navbar-link-color;
    font-weight: 500;
    font-size: $navbar-font-size;
    text-transform: uppercase;
    margin: $navbar-margin;
  }

  a.nav-link:hover {
    color: $navbar-link-color-hover;
    font-weight: 500 !important;
  }

  .icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  //&.active {
  //  a.nav-link {
  //    color: red !important;
  //    text-decoration: underline;
  //  }
  //}
}

.mobile-nav-link {
  color: $navbar-link-color !important;
  font-weight: 500;
  font-size: 15px;
  text-transform: uppercase;
  text-decoration: none !important;
  //margin: $navbar-margin;
}

.mob-m-tb {
  margin: 10px 0px;
}

//Hover underline
.hover-underline-text {
  position: relative;
  display: inline-block;
}

.hover-dropdown-underline-text {
  position: relative;
  display: inline-block;
}

/* Underline effect */
.hover-underline-text::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #2B2B2B, #2B2B2B);
  bottom: 0;
  left: 0;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease-out;
}

.hover-dropdown-underline-text::after {
  content: '';
  position: absolute;
  width: calc(100% + 15px);
  height: 2px;
  background: linear-gradient(to right, #2B2B2B, #2B2B2B);
  bottom: 0;
  left: 0;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease-out;
}

/* Trigger the underline on parent anchor hover */
.hover-underline:hover .hover-underline-text::after {
  transform: scaleX(1);
}

.hover-underline:hover .hover-dropdown-underline-text::after {
  transform: scaleX(1);
}


.hover-underline-text-s {
  position: relative;
  display: inline-block;
  color: #FFFFFF;
  text-decoration: underline;
  padding: 0px;
  text-underline-offset: 6px;

  &:hover {
    text-decoration: none;
  }
}

.hover-underline-text-s::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, #FFFFFF, #FFFFFF);
  bottom: 0;
  left: 0;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease-out;
}

.hover-underline-s:hover .hover-underline-text-s::after {
  transform: scaleX(1);
}

//.hover-underline {
//  cursor: pointer;
//}


//Simple Dropdown Menu
//
//body.no-hover .mega-nav-item:hover > .dropdown-menu {
//  pointer-events: none;
//  opacity: 0;
//}
//
//.dropdown-menu-s {
//  max-height: 0;
//  overflow: hidden;
//  opacity: 0;
//  //transform: translateY(10px);
//  transform: translateY(-16px);
//  transition: max-height 0.5s ease, opacity 0.3s ease, transform 0.3s ease;
//  display: block !important;
//  border-radius: 0px;
//  min-width: 230px;
//  padding: 20px 0px;
//  pointer-events: none;
//}
//
///* Show dropdown on hover (desktop) or show class (mobile click) */
//.dropdown:hover .dropdown-menu-s,
//.dropdown.show .dropdown-menu-s {
//  max-height: 500px;
//  opacity: 1;
//  //transform: translateY(0);
//  //pointer-events: auto;
//}


.simple_dropdown .dropdown-menu {
  border-radius: 0px;
  //min-width: 230px;
  padding: 20px 0px;
  top: 0px !important;
}

/* Base styles for the span */
.simple_dropdown .dropdown-menu li span {
  position: relative;
  display: inline-block;
}

.simple_dropdown .dropdown-menu li a:hover {
  background-color: transparent !important;
}

/* Underline effect */
.simple_dropdown .dropdown-menu li span::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #2B2B2B, #2B2B2B);
  background-color: red !important;
  bottom: 0;
  left: 0;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease-out;
}

/* Trigger the underline on hover */
.simple_dropdown .dropdown-menu li:hover span::after {
  transform: scaleX(1);
}

.mega-nav-item {
  a.nav-link {
    color: $navbar-link-color;
    font-weight: 500;
    font-size: $navbar-font-size;
    margin: $navbar-margin;
    text-transform: uppercase;

    span {
      //padding: 25px 0px;
    }
  }

  a.nav-link:hover {
    color: $navbar-link-color-hover;
    font-weight: 500 !important;
  }
}

///* Custom style to adjust the margin-top of the dropdown menu */
.mega-nav-item.dropdown.position-static.has-submenu .dropdown-menu {
  margin-top: 14px !important;
}

/* Default for desktop (hoverable) */
@media (hover: hover) and (pointer: fine) {
  .mega-nav-item.dropdown.simple_dropdown.has-submenu .dropdown-menu {
    margin-top: $navbar-height !important;
  }
}

/* For touch devices (clickable) */
@media (hover: none) and (pointer: coarse) {
  .mega-nav-item.dropdown.simple_dropdown.has-submenu .dropdown-menu {
    margin-top: $navbar-height !important;
  }
}

//.shadow_bg {
//opacity: 0.7;
//transition: opacity 0.3s ease-in-out;
//background-color: rgba(8, 8, 8, 0.4);
//}

.shadow_bg header {
  //position: relative;
  z-index: 11;
}

.shadow_bg .fixed-overlay {
  position: fixed;
  transition: .8s cubic-bezier(.7, 0, .2, 1);
  transition-property: opacity, visibility, background-color;
  inset-block-start: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-color: rgba(0, 0, 0, .5);
  opacity: 1;
  visibility: visible;
  z-index: 10;
}


.modal {
  height: 100vh;
}

//
//#o_cart_summary {
//  z-index: 1;
//}
//
//#o_wsale_total_accordion {
//  z-index: 1;
//}
//
//.o_website_sale_checkout {
//  padding-top: 24px;
//}

.underline-animation {
  position: relative;
  color: white !important;
  text-decoration: underline;
  padding: 0px;
  text-underline-offset: 6px;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px; // Adjusts the position of the underline
    width: 100%;
    height: 0.5px; // Thickness of the underline
    background-color: white; // Color of the underline
    transform: scaleX(0); // Initially hide the underline
    transform-origin: inherit;
    transition: transform 0.3s ease-in-out;
  }

  &:hover::after {
    transform: scaleX(1);
    //text-decoration: none;
  }
}


// Styles for the menu when it is open in mobile view
.offcanvas.offcanvas-end {
  //makes the slider to appear from left to right
  transform: translateX(-100%);
  //adds animation to slider while closing
  transition: transform 0.4s ease-in-out !important;
}

.offcanvas.offcanvas-start {
  //makes the slider to appear from left to right
  transform: translateX(-100%);
  //adds animation to slider while closing
  transition: transform 0.4s ease-in-out !important;
}

.offcanvas.showing, .offcanvas.show:not(.hiding) {
  transform: none;
  // Maintain the transition effect for a smooth animation
  transition: transform 0.4s ease-in-out;
}

.offcanvas.offcanvas-end {
  top: 0;
  left: 0 !important;
  width: var(--offcanvas-width);
}

.o_header_mobile {
  padding: 10px;
}

.second-mob-view {
  //display: contents;
  display: inline-flex;
  flex-grow: 1;
  justify-content: center;
  width: 50%;

  a {
    margin: 0px;
  }
}

.first-mob-view {
  width: 25%
}

.third-mob-view {
  width: 25%;
  text-align: end;
  white-space: nowrap;
}

.br-0 {
  border-radius: 0px;
}

.pi-50 {
  padding-inline: 50px;
}

.truncate-text {
  display: inline-block;
  max-width: 115px; /* Adjust the width as needed */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table {
  border-color: $light-grey !important;
}

//.o_red_highlight {
//  --background-color: $primary;
//  --bg-solid: $primary !important;
//  box-shadow: 0 0 0 0 $primary;
//  transition: all 0.5s linear;
//}

// Styling mega dropdown

.dropdown-menu {

  //sets background color to mega dropdown
  .o_cc1 {
    background-color: $primary-header !important;
  }

  .dropdown-nav-link {
    color: $navbar-link-color !important;

    &:hover {
      color: $primary !important;
      text-decoration: none;
    }
  }

}

.scroll-container {
  overflow-x: auto;
  overflow-y: hidden;

  &::-webkit-scrollbar {
    width: 12px; /* Width of the vertical scrollbar */
    height: 12px; /* Height of the horizontal scrollbar */
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1; /* Light grey background */
    border-radius: 10px; /* Rounded corners */
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #4CAF50, #8BC34A); /* Gradient green */
    border-radius: 10px; /* Rounded corners */
    border: 2px solid #f1f1f1; /* Adds a border matching the track background */
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #45a049, #7cb342); /* Darker gradient on hover */
  }

  /* For Firefox */
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--link-color-rgb), 1) #f1f1f1;
}

.row.flex-nowrap {
  display: flex;
  flex-wrap: nowrap;
}

.care-card-body {
  padding: 0.5rem;
  text-align: center;
}


//
//.scroll-container {
//  overflow-x: auto;
//  overflow-y: hidden;
//}
//
//.row.flex-nowrap {
//  display: flex;
//  flex-wrap: nowrap;
//}
//
///* Target the scrollbar within the scroll-container */
//.scroll-container::-webkit-scrollbar {
//  width: 12px; /* Width of the vertical scrollbar */
//  height: 12px; /* Height of the horizontal scrollbar */
//}
//
///* Track (the area behind the scrollbar) */
//.scroll-container::-webkit-scrollbar-track {
//  background: #f1f1f1; /* Light grey background */
//  border-radius: 10px; /* Rounded corners */
//}
//
///* Handle (the draggable part of the scrollbar) */
//.scroll-container::-webkit-scrollbar-thumb {
//  background: linear-gradient(180deg, #4CAF50, #8BC34A); /* Gradient green */
//  border-radius: 10px; /* Rounded corners */
//  border: 2px solid #f1f1f1; /* Adds a border matching the track background */
//}
//
///* Handle on hover */
//.scroll-container::-webkit-scrollbar-thumb:hover {
//  background: linear-gradient(180deg, #45a049, #7cb342); /* Darker gradient on hover */
//}


@each $key, $value in $spacing-values {
  .mb-#{$key} {
    margin-bottom: $value !important;
  }
  .mt-#{$key} {
    margin-top: $value !important;
  }
  .mr-#{$key} {
    margin-right: $value !important;
  }
  .ml-#{$key} {
    margin-left: $value !important;
  }
  .pb-#{$key} {
    padding-bottom: $value !important;
  }
  .pt-#{$key} {
    padding-top: $value !important;
  }

}


// Media Query for Navbar on Larger Screens
@media (min-width: 992px) {
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 1vw;
    padding-left: 1vw;
  }


  //@each $key, $value in $spacing-values {
  //  .mb-#{$key} {
  //    margin-bottom: $value !important;
  //  }
  //  .mt-#{$key} {
  //    margin-top: $value !important;
  //  }
  //  .mr-#{$key} {
  //    margin-right: $value !important;
  //  }
  //  .ml-#{$key} {
  //    margin-left: $value !important;
  //  }
  //  .pb-#{$key} {
  //    padding-bottom: $value !important;
  //  }
  //  .pt-#{$key} {
  //    padding-top: $value !important;
  //  }
  //
  //}

  .o_file_wrap {
    .pt-1 {
      padding-top: 0.25rem !important;
    }

    .mt-1 {
      margin-top: 0.25rem !important;
    }
  }
}


.ptb-10 {
  padding-top: 15rem !important;
  padding-bottom: 5rem !important;
}

.pb50 {
  padding-bottom: 50px;
}

.pt70 {
  padding-top: 70px;
}

//
//@media (min-width: 933px) {
//  .offcanvas.offcanvas-end {
//    top: 0;
//    left: 0 !important;
//    width: 100%;
//  }
//}

//@keyframes slideDown {
//  from {
//    transform: translateY(-10%);
//    opacity: 0;
//  }
//  to {
//    transform: translateY(0);
//    opacity: 1;
//  }
//}
//
//.dropdown-menu {
//  //animation: slideDown 2.3s ease-in-out !important;
//  transform: none !important;
//  transition: transform 0.6s ease-in-out !important;
//}
//
//@media (min-width: 992px) {
//  .dropdown:hover .dropdown-menu {
//    display: block !important;
//  }
//}


// Edit google address autocomplete dropdown

#div_street {
  .dropdown-menu {

    a {
      color: $primary-text-color
    }

    img {
      display: none;
    }
  }
}


.o_cart_product {
  a {
    color: $primary-text-color;
  }
}