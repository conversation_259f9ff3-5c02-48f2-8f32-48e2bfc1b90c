#footer {
  //background-color: $footer-background;
  //color: $footer-text-color;

  //a {
  //  color: $footer-link-color;
  //  text-decoration: none;
  //
  //  &:hover {
  //    color: $footer-link-hover-color;
  //    text-decoration: underline;
  //  }
  //}

  a {
    color: $footer-link-color;
    text-decoration: none;
    position: relative;
    display: inline-block;

    &::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 2px;
      background: $footer-link-hover-color;
      bottom: 0;
      left: 0;
      transform: scaleX(0);
      transform-origin: left;
      transition: transform 0.3s ease-out;
    }

    &:hover::after,
    &:focus::after {
      transform: scaleX(1);
    }

    &:hover,
    &:focus {
      color: $footer-link-hover-color;
    }
  }

  h4 {
    margin-bottom: 20px;
  }

  .contact {
    margin: 5px 0px 20px 0px;
  }
}


.footer-social-links {
  display: flex;
  gap: 20px;

  a {
    color: $footer-text-color;
    text-decoration: none;
    font-size: 24px;

    &:hover {
      color: $footer-link-hover-color;
    }
  }

  .fa {
    color: $grey-color;
    font-size: 24px !important;

    &:hover {
      color: $footer-link-hover-color;
    }
  }
}
