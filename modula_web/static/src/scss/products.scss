.oe_website_sale {
  padding-top: 15px !important;
}

//Breadcrumb category name

.o_wsale_products_main_row {
  .breadcrumb {
    justify-content: center;

    a {
      color: $primary-text-color;

      &:hover {
        color: $primary;
        text-decoration: none;
      }
    }

    .fa {
      margin-inline: 15px;
    }
  }
}

//Product list product name
.o_wsale_products_item_title {
  a {
    color: $primary-text-color !important;

    &:hover {
      color: $primary-color;
      text-decoration: none;
    }
  }
}


.o_wsale_filmstip_container {
  justify-content: center;
  min-height: 78px;

  .chips {
    border: 1px solid $light-grey;
    border-radius: 0px;
    background: transparent;

    &:hover {
      border: 1px solid $primary;
      background-color: $primary;
      color: #ffffff;
    }
  }

  .border-primary {
    border: 1px solid $primary !important;
    background-color: $primary !important;
    color: #ffffff !important;
  }

  .o_wsale_filmstip_wrapper {
    margin-bottom: 1.5rem !important;
    scroll-snap-type: x mandatory;
    cursor: grab;
    scrollbar-width: thin;
  }
}

.wishlist-icon {
  position: absolute;
  top: 0;
  right: 0;

  button {
    background-color: transparent;
    border: none;
    cursor: pointer;

    i {
      &::before {
        content: "\f08a"; // Unicode for fa-heart-o (outlined heart)
      }
    }

    &:hover:not(:disabled) {
      i::before {
        content: "\f004"; // Unicode for fa-heart (solid heart)
        color: $primary; // Use the primary theme color
      }
    }

    &:disabled {
      opacity: 0.8;
      cursor: not-allowed;
      background-color: transparent;

      i::before {
        content: "\f004"; // Unicode for fa-heart (solid heart)
        color: $primary; // Color for disabled state
      }
    }
  }

}

.no-product-border {
  border: 1px dashed $light-grey;
  padding: 80px;
}

//Product Details

#product_details {
  background-color: $product-bg;
  //padding: 6rem;
  padding: 5vw;

  .variant_attribute .attribute_name {
    text-transform: none;
  }

  .variant_attribute {

    .pills_attribute {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }


    .o_variant_pills {
      cursor: pointer !important;
      //padding: 0.5rem 1.5rem;
      padding: 0.5rem;
      font-size: 1rem;
      border-radius: 30px;
      color: $pills-color;
      background-color: transparent;
      border: 1px solid $light-grey;

      &:hover,
      &.active {
        background-color: $pills-background !important;
        color: $black-color;
        box-shadow: 0 0 0 2px $pills-color;

        .badge {
          color: $black-color;
          box-shadow: 0 0 0 2px $pills-color;
        }

      }

    }


    //price Badge
    .badge {
      border: 1px solid $light-grey !important;
      background-color: $product-bg !important;
    }

    //For Color attribute
    .color_attribute {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 10px 10px 20px;
      max-height: 171px;
      background: white;
      border: 1px solid $light-grey;
      scroll-behavior: smooth;
    }

    .css_attribute_color {
      position: relative;
      display: inline-block;
      //border: 0px solid #ffffff;
      border: none;
      border-radius: 0% !important;
      text-align: center;

      .color-name {
        position: absolute;
        bottom: -17px;
        left: 0px;
        justify-content: center;
        text-align: center;
        font-size: 10px;
      }

      &:hover,
      &.active {
        background-color: $pills-background;
        color: $black-color;
        box-shadow: 0 0 0 2px $pills-color;

        .truncate-text {
          color: $black-color !important;
        }

      }

      &::before {
        //content: "";
        //position: absolute;
        //top: -3px;
        //left: -3px;
        //right: -3px;
        //bottom: -3px;
        border: 0 solid white;
        border-radius: 0%;
        box-shadow: inset 0 0 3px transparent;
      }

      // Styles for the input element
      input {
        //margin: 8px;
        height: 100px;
        width: 100px;
        //opacity: 0;

        &:hover {
          cursor: pointer;
        }
      }
    }

  }

  #add_to_cart {
    border-radius: 30px !important;
    padding: 0.6rem;
  }

  .sku_list {

    .o_wsale_product_attribute {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      .skus_pills {
        border: 1px solid $light-grey;
        border-radius: 30px;
        text-transform: capitalize;
        width: 100%;
        height: 100%;
        text-align: center;
        padding: 0.6rem;
        font-size: 1rem;

        &:hover,
        &.active {
          background-color: $pills-background;
          color: $black-color;
          box-shadow: 0 0 0 2px $pills-color;
        }
      }
    }
  }

}

//Product Content
.product_content {
  padding: 10%;
}

//  Accordion
.oe_website_sale .accordion-button:not(.collapsed)::after {
  background-image: none !important;
}

.accordion-collapse {
  transition: height 0.5s ease;
}


.marketing_content {
  .grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 6vh;

    //.zoom-hover {
    //  transition: transform 0.3s ease;
    //}
    //
    //.zoom-hover:hover {
    //  transform: scale(1.03);
    //}

    .image-wrapper {
      overflow: hidden;
      display: block;
    }

    .zoom-hover {
      width: 100%;
      height: auto;
      transition: transform 0.3s ease;
      display: block;
    }

    .zoom-hover:hover {
      transform: scale(1.03);
      transform-origin: center center;
    }

  }

  .grid-item {
    h3 {
      font-size: 28px;
    }
  }

  .pdp-m-6 {
    margin-top: 6vh;
  }

}

.dimension_img img {
  //width: 45%;
  width: 25vw;
}

@media (max-width: 1280px) {

  #product_details {
    //padding: 2rem;

    .variant_attribute {

      .pills_attribute {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    .sku_list {

      .o_wsale_product_attribute {
        grid-template-columns: repeat(2, 1fr) !important;
      }
    }
  }

  .o_wsale_products_grid_table_wrapper {
    .grid {
      grid-template-columns: repeat(var(--columns, 9), 1fr) !important;
    }
  }

  .dimension_img img {
    //width: 50%;
    width: 40vw;
  }
}


@media (max-width: 990px) {
  .marketing_content {
    .grid-container {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 3vh;
    }

    .grid-item {
      h3 {
        font-size: 28px;
      }
    }

    .pdp-m-6 {
      margin-top: 5vh;
    }
  }

  .dimension_img img {
    //width: 40%;
    width: 35vw;
  }
}

@media (max-width: 768px) {

  #product_details {
    //padding: 2rem;


    .variant_attribute {

      //.pills_attribute {
      //  grid-template-columns: repeat(1, 1fr);
      //}

      pills_attribute_cover {
        grid-template-columns: repeat(2, 1fr);
      }

      .o_variant_pills {
        font-size: $mobile-font-size;
      }
    }

    .sku_list {

      .o_wsale_product_attribute {
        grid-template-columns: repeat(1, 1fr) !important;

        .skus_pills {
          font-size: $mobile-font-size;
        }
      }
    }
  }

  .accordion {
    .accordion-button {
      padding: 1rem 0.2rem;
    }

    .accordion-body {
      padding: 1rem 0.2rem;
      font-size: $mobile-font-size;
    }

    .h6-fs {
      font-size: $mobile-font-size;
    }
  }

  .product_content {
    padding: 10% 15px;

    p, h6 {
      font-size: $mobile-font-size;
    }

    .h3-fs {
      font-size: calc(1.325rem + 0.5vw);
    }
  }

  .oe_product_cart .oe_product_image .oe_product_image_link {
    aspect-ratio: 1 / 1 !important;
  }


  .o_wsale_products_grid_table_wrapper {
    .grid {
      grid-template-columns: repeat(var(--columns, 12), 1fr) !important;
    }
  }

  .marketing_content {
    .grid-container {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 2vh;
    }


    .pdp-m-6 {
      margin-top: 0;
    }

  }

  .oe_product_cart {
    .oe_product_image .oe_product_image_link {
      //aspect-ratio: 4 / 3;

      .oe_product_image_img_wrapper {
        //min-height: 200px;

        img {
          object-fit: contain !important;
        }
      }
    }
  }


  .dimension_img img {
    //width: 55%;
    width: 60vw;
  }

}

.product_price {
  font-family: Inter !important;
}


// Product Item
.oe_product_cart {

  border: 1px solid transparent;
  transition: border-color 0.6s ease;

  .oe_product_image .oe_product_image_link {
    aspect-ratio: 4 / 3;

    .oe_product_image_img_wrapper {
      min-height: 200px;

      img {
        object-fit: cover;
      }
    }
  }

  //  .oe_product_image .oe_product_image_link {
  //
  //  .oe_product_image_img_wrapper {
  //
  //    img {
  //      object-fit: cover;
  //      height: 100% ;
  //    }
  //  }
  //}

  .o_wsale_product_information {
    //padding: 15px;
    padding: 1vh;
  }

  img {
    //opacity: 1;
    //transition: opacity 1s ease-out !important;
    height: auto;
  }

  &:hover {
    border-color: $light-grey;
    //box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

    //a{
    //  font-weight: 400;
    //}
  }
}


// product Grid
.o_wsale_products_grid_table_wrapper {
  .grid {
    display: grid;
    grid-template-rows: repeat(var(--rows, 1), 1fr);
    grid-template-columns: repeat(var(--columns, 12), 1fr);
    gap: var(--gap, 15px);
  }
}

// product image display
.oe_product_image_link {
  position: relative;

  .product-default-image {
    display: block;
    width: 100%;
    transition: opacity .4s ease-in-out, visibility .4s ease-in-out;
  }

  .product-hover-image {
    display: none;
    //width: 100%;
    //position: absolute;
    top: 0;
    left: 0;
  }

  &:hover {
    .product-default-image {
      display: none;
    }

    .product-hover-image {
      display: block;
      transition: opacity .4s ease-in-out, visibility .4s ease-in-out;
    }
  }

}
